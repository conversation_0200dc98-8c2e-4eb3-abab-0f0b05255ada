/**
 * API Response Types
 * Based on the OpenAPI specification for 进化者课件授权平台
 */

// Base API response structure
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
  timestamp: number;
  success: boolean;
}

// Region API specific types
export type ProvinceListResponse = ApiResponse<string[]>;
export type CityListResponse = ApiResponse<string[]>;

// Error response type
export interface ApiError {
  code: number;
  message: string;
  timestamp: number;
  success: false;
}

// Request/Response types for region endpoints
export interface GetCityListParams {
  province: string;
}

// Agent API types
export interface CourseAgentSearchReq {
  province?: string;
  city?: string;
  area?: string;
  agenId?: number; // API文档中的字段名（可能是拼写错误）
  agentName?: string; // 代理商名称字段
  sn?: string;
  stageType?: 'UNKNOWN' | 'PRESCHOOL' | 'PRIMARY';
  courseId?: number;
  robotIds?: string[];
}

export interface AgentVO {
  agentId: number;
  agentName: string;
  province: string;
  city: string;
  area: string;
  region: string;
  bots: number;
}

export interface AgentUsedName {
  id: number;
  agentsId: number;
  usedName: string;
}

export type AgentListResponse = ApiResponse<AgentVO[]>;
export type AgentUsedNameListResponse = ApiResponse<AgentUsedName[]>;

// Agent Detail API types
export interface OrderSnLocation {
  id: number;
  robotId: string;
  province: string;
  city: string;
  area: string;
  sn: string;
}

export interface AgentDetail {
  orderSnLocations: OrderSnLocation[];
}

export type AgentDetailResponse = ApiResponse<AgentDetail>;

// SN Management API types
export interface SupplementSnReq {
  robotId: string;
  sn: string;
}

export type SupplementSnResponse = ApiResponse<void>;

// Course API types
export interface CourseGrade {
  id: number;
  gradeName: string;
  status: number;
  exp1?: string;
}

export type CourseListResponse = ApiResponse<CourseGrade[]>;

// Course Authorization API types
export interface CourseAuthorizationReq extends CourseAgentSearchReq {
  courseId?: number; // 课程ID，单个课程授权时需要
}

export type CourseAuthorizationResponse = ApiResponse<void>;
