"use client";

import { ArrowDown } from "lucide-react";
import { AgentIcon } from "@/components/ui/agent-icon";
import { But<PERSON> } from "@/components/ui/button";
import { AgentSelect } from "@/components/ui/agent-select";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { useState, useEffect } from "react";
import { toastService } from "@/services/toast";
import { agentService } from "@/services/agent";
import { AgentUsedName } from "@/types/api";

interface CompanyEditDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  currentCompany?: string;
  currentAgentId?: number;
  onConfirm: (agentId: number, agentName: string) => void;
  title?: string;
  placeholder?: string;
  stageType?: 'UNKNOWN' | 'PRESCHOOL' | 'PRIMARY';
}

export function CompanyEditDialog({
  open,
  onOpenChange,
  currentCompany = "",
  currentAgentId,
  onConfirm,
  title = "机器人归属信息变更",
  placeholder = "请选择代理商",
  stageType = 'PRIMARY'
}: CompanyEditDialogProps) {
  const [selectedAgentId, setSelectedAgentId] = useState<number | undefined>(currentAgentId);
  const [selectedAgentName, setSelectedAgentName] = useState(currentCompany);
  const [agents, setAgents] = useState<AgentUsedName[]>([]);
  const [loading, setLoading] = useState(false);

  // 加载代理商列表
  useEffect(() => {
    const loadAgents = async () => {
      try {
        setLoading(true);
        const agentList = await agentService.getAgentList(stageType);
        setAgents(agentList);
      } catch (error) {
        console.error('Failed to load agents:', error);
        toastService.error('加载代理商列表失败');
      } finally {
        setLoading(false);
      }
    };

    if (open) {
      loadAgents();
    }
  }, [open, stageType]);

  // 当弹窗打开时重置选择值
  useEffect(() => {
    if (open) {
      setSelectedAgentId(currentAgentId);
      setSelectedAgentName(currentCompany);
    }
  }, [open, currentAgentId, currentCompany]);

  const handleAgentSelect = (agentId: number, agentName: string) => {
    setSelectedAgentId(agentId);
    setSelectedAgentName(agentName);
  };

  const handleConfirm = () => {
    if (selectedAgentId && selectedAgentName) {
      onConfirm(selectedAgentId, selectedAgentName);
      toastService.success("机器人归属信息已成功更新");
      onOpenChange(false);
    } else {
      toastService.error("请选择一个代理商");
    }
  };

  const handleCancel = () => {
    setSelectedAgentId(currentAgentId);
    setSelectedAgentName(currentCompany);
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="w-[360px] max-w-none mx-auto bg-background/50 backdrop-blur-sm">
        <DialogHeader>
          <DialogTitle className="text-center">{title}</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4 py-4">
          <div className="flex justify-center">
            <div className="relative">
              <div className="flex items-center gap-2 h-12 px-4">
                <AgentIcon color="#f97316" className="size-8" />
                <span className="text-sm text-muted-foreground">
                  &lt; 暂无归属代理商 &gt;
                </span>
              </div>
              <div className="absolute bottom-0 left-4 right-4 h-px bg-green-500" />
            </div>
          </div>
          
          <div className="flex justify-center">
            <div className="w-12 h-12 rounded-full flex items-center justify-center">
              <ArrowDown className="w-12 h-12 text-green-500" />
            </div>
          </div>
          
          <div className="flex justify-center">
            <div className="relative">
              <div className="flex items-center gap-2 h-8 px-4">
                <AgentIcon color="#f97316" className="size-8" />
                <AgentSelect
                  value={selectedAgentId?.toString() || ""}
                  onChange={handleAgentSelect}
                  placeholder={loading ? "加载中..." : placeholder}
                  agents={agents}
                  className="w-40"
                  height="h-[28px]"
                />
              </div>
              <div className="absolute bottom-0 left-4 right-4 h-px bg-green-500" />
            </div>
          </div>
        </div>
        
        <div className="flex justify-center items-center gap-4 mt-6">
          <Button 
            onClick={handleConfirm}
            className="bg-green-600 text-white hover:bg-green-700 border-green-600 rounded-full px-6 py-2"
          >
            确认更改
          </Button>
          <Button 
            onClick={handleCancel}
            className="bg-red-600 text-white hover:bg-red-700 border-red-600 rounded-full px-6 py-2"
          >
            取消更改
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}