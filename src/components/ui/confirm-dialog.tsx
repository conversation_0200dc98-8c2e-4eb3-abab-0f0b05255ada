"use client";

import * as React from "react";
import { Di<PERSON>, Dialog<PERSON>ontent, Di<PERSON><PERSON>eader, DialogTitle } from "./dialog";
import { Button } from "./button";
import { AlertCircle } from "lucide-react";

interface ConfirmDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
  message: string;
  onConfirm: () => void;
  confirmText?: string;
  cancelText?: string;
}

export function ConfirmDialog({
  open,
  onOpenChange,
  title,
  message,
  onConfirm,
  confirmText = "确定",
  cancelText = "取消"
}: ConfirmDialogProps) {
  const handleConfirm = () => {
    onConfirm();
    onOpenChange(false);
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="w-[360px] max-w-none mx-auto bg-background/50 backdrop-blur-sm">
        <DialogHeader>
          <DialogTitle className="text-center">{title}</DialogTitle>
        </DialogHeader>
        
        <div className="flex flex-col items-center justify-center py-6 space-y-4">
          <div className="flex justify-center items-center">
            <AlertCircle className="w-16 h-16 text-amber-500" />
          </div>
          
          <div className="text-center">
            <p className="text-sm text-muted-foreground">{message}</p>
          </div>
        </div>
        
        <div className="flex justify-center items-center gap-4 mt-6">
          {cancelText && (
            <Button 
              onClick={handleCancel}
              className="bg-red-600 text-white hover:bg-red-700 border-red-600 rounded-full px-6 py-2"
            >
              {cancelText}
            </Button>
          )}
          <Button 
            onClick={handleConfirm}
            className="bg-green-600 text-white hover:bg-green-700 border-green-600 rounded-full px-6 py-2"
          >
            {confirmText}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
