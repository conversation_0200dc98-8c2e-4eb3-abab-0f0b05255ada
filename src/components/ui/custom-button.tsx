"use client";

import { forwardRef } from "react";
import { cn } from "@/lib/utils";

export interface CustomButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
}

const CustomButton = forwardRef<HTMLButtonElement, CustomButtonProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <button
        className={cn(
          "inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
          "h-[28px] w-[60px] text-white hover:bg-black/80",
          className
        )}
        style={{
          background: 'rgba(110, 226, 87, 1)',
          fontSize: '14px',
          fontWeight: 500,
          lineHeight: '28px',
          color: 'rgba(0, 0, 0, 1)',
          borderRadius: '14px',
        }}
        ref={ref}
        {...props}
      >
        {children}
      </button>
    );
  }
);
CustomButton.displayName = "CustomButton";

export { CustomButton };