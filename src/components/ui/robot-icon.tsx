import { cn } from "@/lib/utils";

interface RobotIconProps {
    color?: string;
    className?: string;
}

export function RobotIcon({ color = "currentColor", className }: RobotIconProps) {
    return (
        <svg
            className={cn("size-6", className)}
            xmlns="http://www.w3.org/2000/svg"
            width="50"
            height="50"
            viewBox="0 0 50 50"
            fill="none"
        >



            <path
            
                 fill={color}
            d="M29.432 50L29.2047 49.8862L28.8638 49.6592C28.182 49.3183 27.7274 48.75 27.5001 48.0682C27.2728 47.3864 27.3865 46.7045 27.7274 46.1364C27.9547 45.7954 28.182 45.4545 28.5228 45.2273L21.8183 45.2273C22.6138 45.7954 22.9547 46.5909 22.9547 47.5C22.8411 48.6364 22.2728 49.4316 21.2501 49.8862C21.1365 49.8862 21.0228 50 21.0228 50L19.7728 50C18.9774 49.6592 18.0683 49.2046 17.7274 48.0682C17.3865 46.9318 17.8411 46.0227 18.8638 45.1136L17.7274 45.1136C16.932 45.1136 16.1365 45.1136 15.4547 44.8864C12.5001 44.3182 10.5683 41.5909 10.7956 38.6364C11.1365 35 11.5911 31.25 12.0456 27.3864C12.3865 24.5455 15.0001 22.2727 17.9547 22.2727L32.2728 22.2727C35.4547 22.2727 37.9547 24.6591 38.2956 27.8409C38.6365 31.3636 39.0911 34.7727 39.432 38.2954C39.6592 40 39.0911 41.7045 38.0683 42.9545C37.0456 44.2045 35.4547 45 33.7501 45.1136L31.4774 45.1136L31.5911 45.2272C31.932 45.5682 32.2728 45.9091 32.5001 46.3636C32.8411 46.9318 32.8411 47.6136 32.6138 48.1818C32.3865 48.8638 31.932 49.4316 31.2501 49.7729L30.4547 50L29.432 50ZM29.8865 42.2727L33.0683 42.2727C34.2047 42.2727 35.1138 41.9318 35.682 41.1364C36.2501 40.4545 36.5911 39.5454 36.4774 38.5227C36.1365 35 35.682 31.5909 35.3411 28.0682C35.1138 26.3636 33.8638 25.1136 32.1592 25.1136L17.9547 25.1136C16.3638 25.1136 15.0001 26.25 14.7728 27.8409C14.3183 31.4773 13.9774 35.1136 13.6365 38.75C13.6365 39.0909 13.6365 39.4318 13.7501 39.7727C14.0911 41.25 15.3411 42.1591 17.1592 42.1591L25.1138 42.1591L29.8865 42.2727L29.8865 42.2727ZM15.7956 20.7955C14.8865 20.7955 14.6592 20.5682 14.6592 19.6591L14.6592 17.3864L14.6592 1.02273C14.6592 0.795454 14.7728 0.568183 14.8865 0.340909C14.8865 0.227273 15.0001 0.113636 15.0001 0.113636L15.0001 0L35.0001 0L35.0001 0.113636C35.0001 0.227273 35.1138 0.340909 35.1138 0.340909C35.2274 0.568183 35.3411 0.795454 35.3411 1.02273L35.3411 19.6591C35.3411 20.5682 35.1138 20.7955 34.2047 20.7955L15.7956 20.7955ZM32.6138 17.9545L32.6138 2.95455L17.5001 2.95455L17.5001 18.0682L32.6138 18.0682L32.6138 17.9545ZM3.9774 38.4091C3.9774 38.0682 4.09104 37.7273 4.09104 37.3864C4.20468 36.5909 4.31832 35.7954 4.43195 35.1136C4.88651 32.5 5.79559 30.5682 7.15921 28.8636C8.18197 27.7273 9.31831 26.8182 10.7956 26.3636L11.3638 26.1364L11.1365 26.8182C11.1365 26.9318 11.0229 27.0455 11.0229 27.1591C8.86377 31.5909 6.8183 36.0227 4.77286 40.4545C4.65922 40.5682 4.54559 40.6818 4.43195 40.9091L4.31832 41.0227L3.86377 41.7045L3.86377 38.4091L3.9774 38.4091ZM45.5683 14.8864L46.0228 15C46.1365 18.1818 46.0228 22.3864 43.6365 26.0227C42.7274 27.5 41.4774 28.5227 39.8865 29.2045C39.7728 29.2045 39.6592 29.3182 39.432 29.3182L38.6365 29.6591L45.5683 14.8864ZM26.3638 6.81816L30.682 6.81816L30.682 11.0227L26.3638 11.0227L26.3638 6.81816ZM19.5456 6.81816L23.7501 6.81816L23.7501 11.0227L19.5456 11.0227L19.5456 6.81816Z"  >
            </path>

        </svg>
    );
}