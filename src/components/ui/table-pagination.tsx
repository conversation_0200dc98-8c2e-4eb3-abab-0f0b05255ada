"use client";

import { cn } from "@/lib/utils";
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from "@/components/ui/pagination-zh";

interface TablePaginationProps {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  pageSize: number;
  onPageChange: (page: number) => void;
  className?: string;
}

export function TablePagination({
  currentPage,
  totalPages,
  totalItems,
  pageSize,
  onPageChange,
  className
}: TablePaginationProps) {
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = Math.min(startIndex + pageSize, totalItems);

  // 如果总页数不足1页，则不显示分页组件
  if (totalPages <= 1) {
    return (
      <div className={cn("flex items-center justify-between px-6 py-2", className)}>
        <div className="text-sm text-gray-400 whitespace-nowrap">
          显示 {startIndex + 1}-{endIndex} 条，共 {totalItems} 条
        </div>
      </div>
    );
  }

  return (
    <div className={cn("flex items-center justify-between px-6 py-2", className)}>
      <div className="text-sm text-gray-400 whitespace-nowrap">
        显示 {startIndex + 1}-{endIndex} 条，共 {totalItems} 条
      </div>
      <Pagination className="justify-end">
        <PaginationContent className="gap-1">
          <PaginationItem>
            <PaginationPrevious 
              onClick={() => onPageChange(Math.max(1, currentPage - 1))}
              className={cn(
                "bg-transparent border-gray-600 text-gray-400 hover:bg-white/10 hover:text-white h-8 text-xs",
                currentPage === 1 && "opacity-50 cursor-not-allowed"
              )}
            />
          </PaginationItem>
          
          {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
            <PaginationItem key={page}>
              <PaginationLink
                onClick={() => onPageChange(page)}
                isActive={currentPage === page}
                size="sm"
                className={cn(
                  "bg-transparent border-gray-600 text-gray-400 hover:bg-white/10 hover:text-white h-8 w-8 text-xs",
                  currentPage === page && "bg-blue-600 text-white hover:bg-blue-500 border-blue-600"
                )}
              >
                {page}
              </PaginationLink>
            </PaginationItem>
          ))}
          
          <PaginationItem>
            <PaginationNext 
              onClick={() => onPageChange(Math.min(totalPages, currentPage + 1))}
              className={cn(
                "bg-transparent border-gray-600 text-gray-400 hover:bg-white/10 hover:text-white h-8 text-xs",
                currentPage === totalPages && "opacity-50 cursor-not-allowed"
              )}
            />
          </PaginationItem>
        </PaginationContent>
      </Pagination>
    </div>
  );
}