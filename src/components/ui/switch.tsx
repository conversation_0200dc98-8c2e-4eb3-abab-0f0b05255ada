"use client"

import * as React from "react"
import * as SwitchPrimitives from "@radix-ui/react-switch"

import { cn } from "@/lib/utils"

const Switch = React.forwardRef<
  React.ElementRef<typeof SwitchPrimitives.Root>,
  React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root> & {
    checkedText?: string;
    uncheckedText?: string;
  }
>(({ className, checkedText, uncheckedText, ...props }, ref) => {
  const [isChecked, setIsChecked] = React.useState(props.checked || false);
  
  React.useEffect(() => {
    setIsChecked(props.checked || false);
  }, [props.checked]);

  return (
    <SwitchPrimitives.Root
      className={cn(
        "peer inline-flex h-6 w-24 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",
        className
      )}
      {...props}
      ref={ref}
      onCheckedChange={(checked) => {
        setIsChecked(checked);
        props.onCheckedChange?.(checked);
      }}
    >
      <SwitchPrimitives.Thumb
        className={cn(
          "pointer-events-none flex items-center justify-center h-5 w-12 rounded-full shadow-xl ring-0 transition-all duration-200 text-xs font-medium text-white",
          "data-[state=checked]:translate-x-11 data-[state=unchecked]:translate-x-0.5",
          "data-[state=checked]:bg-red-800 data-[state=unchecked]:bg-green-800"
        )}
      >
        <span className="whitespace-nowrap">
          {isChecked ? (checkedText || "ON") : (uncheckedText || "OFF")}
        </span>
      </SwitchPrimitives.Thumb>
    </SwitchPrimitives.Root>
  );
})
Switch.displayName = SwitchPrimitives.Root.displayName

export { Switch }