"use client";

import { forwardRef } from "react";
import { cn } from "@/lib/utils";

export interface CustomInputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {}

const CustomInput = forwardRef<HTMLInputElement, CustomInputProps>(
  ({ className, type, ...props }, ref) => {
    return (
      <input
        type={type}
        className={cn(
          "flex h-[37px] w-full rounded-full px-4 py-2 text-sm text-white placeholder:text-gray-500 focus:outline-none focus:ring-2 focus:ring-white/50 disabled:cursor-not-allowed disabled:opacity-50",
          className
        )}
        style={{
          background: 'var(--custom-input-bg, rgba(43, 44, 44, 0.55))',
          border: '1px solid var(--custom-input-border, rgb(132, 132, 132))',
          backdropFilter: 'blur(50px)',
        }}
        ref={ref}
        {...props}
      />
    );
  }
);
CustomInput.displayName = "CustomInput";

export { CustomInput };