"use client";

import { useState, useEffect, useRef } from "react";
import { ChevronDown, Search, X } from "lucide-react";
import { AgentUsedName } from "@/types/api";

interface AgentSelectProps {
  value?: string;
  onChange: (agentsId: number, usedName: string) => void;
  placeholder?: string;
  agents: AgentUsedName[];
  className?: string;
  height?: string;
}

export function AgentSelect({
  value = "",
  onChange,
  placeholder = "请选择代理商",
  agents,
  className = "",
  height = "h-[28px]"
}: AgentSelectProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [displayValue, setDisplayValue] = useState(value);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // 过滤代理商列表
  const filteredAgents = agents.filter(agent =>
    agent.usedName.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // 处理点击外部关闭下拉框
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setSearchTerm("");
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // 当 value 改变时更新显示值
  useEffect(() => {
    if (value) {
      const selectedAgent = agents.find(agent => agent.agentsId.toString() === value);
      setDisplayValue(selectedAgent?.usedName || value);
    } else {
      setDisplayValue("");
    }
  }, [value, agents]);

  const handleInputClick = () => {
    setIsOpen(true);
    setSearchTerm(displayValue);
  };

  const handleInputFocus = () => {
    setIsOpen(true);
    setSearchTerm(displayValue);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    if (!isOpen) {
      setIsOpen(true);
    }
  };

  const handleAgentSelect = (agent: AgentUsedName) => {
    setDisplayValue(agent.usedName);
    onChange(agent.agentsId, agent.usedName);
    setIsOpen(false);
    setSearchTerm("");
  };

  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation();
    setDisplayValue("");
    onChange(0, "");
    setSearchTerm("");
  };

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      <div className="relative">
        <div className="relative w-full">
          <input
            ref={inputRef}
            type="text"
            value={isOpen ? searchTerm : displayValue}
            onChange={handleInputChange}
            onFocus={handleInputFocus}
            placeholder={placeholder}
            className={`w-full bg-transparent border-none outline-none text-white placeholder:text-gray-500 focus:ring-2 focus:ring-white/50 pl-4 pr-10 ${height}`}
            style={{
              backgroundColor: 'rgba(43, 44, 44, 0.55)',
              border: '1px solid rgb(132, 132, 132)',
              borderRadius: '9999px',
              backdropFilter: 'blur(50px)',
            }}
          />
          <button
            onClick={handleClear}
            className={`absolute right-3 top-1/2 transform -translate-y-1/2 transition-all duration-200 ${
              displayValue 
                ? 'text-gray-400 hover:text-white opacity-100 pointer-events-auto' 
                : 'text-transparent opacity-0 pointer-events-none'
            }`}
            type="button"
          >
            <X size={16} />
          </button>
        </div>
      </div>

      {isOpen && (
        <div 
          className="absolute z-50 w-full mt-1 rounded-lg shadow-lg max-h-60 overflow-auto"
          style={{
            background: 'var(--custom-input-bg, rgba(43, 44, 44, 0.55))',
            border: '1px solid var(--custom-input-border, rgb(132, 132, 132))',
            backdropFilter: 'blur(50px)',
          }}
        >
          {filteredAgents.length > 0 ? (
            filteredAgents.map((agent) => (
              <div
                key={agent.id}
                className="px-4 py-2 text-sm cursor-pointer text-white hover:bg-white/10 transition-colors"
                onClick={() => handleAgentSelect(agent)}
              >
                <span>{agent.usedName}</span>
              </div>
            ))
          ) : (
            <div className="px-4 py-2 text-sm text-gray-400 flex items-center gap-2">
              <Search className="w-4 h-4" />
              <span>未找到匹配的代理商</span>
            </div>
          )}
        </div>
      )}
    </div>
  );
}