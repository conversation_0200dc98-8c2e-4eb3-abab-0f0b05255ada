import { cn } from "@/lib/utils";

interface LocationIconProps {
    color?: string;
    className?: string;
}

export function LocationIcon({ color = "currentColor", className }: LocationIconProps) {
    return (
        <svg
            className={cn("size-6", className)}
            xmlns="http://www.w3.org/2000/svg"
            width="50"
            height="50"
            viewBox="0 0 50 50"
            fill="none"
        >
            <path d="M24.9975 12.2545C21.3984 12.2545 18.4707 15.1294 18.4707 18.6623C18.4707 22.1945 21.3984 25.0665 24.9975 25.0665C28.5965 25.0665 31.5229 22.1945 31.5229 18.6623C31.5229 15.1293 28.5965 12.2545 24.9975 12.2545ZM24.9975 22.2291C22.9967 22.2291 21.369 20.6286 21.369 18.6608C21.369 16.6938 22.9967 15.0926 24.9975 15.0926C26.9968 15.0926 28.6244 16.6938 28.6244 18.6608C28.6244 20.6286 26.9968 22.2291 24.9975 22.2291Z" fill="#C4C4C4" >
            </path>
            <path d="M40.2742 19.6135C40.2742 11.34 33.4213 4.60971 24.9979 4.60971C16.5761 4.60971 9.72607 11.34 9.72607 19.6135C9.72607 26.102 19.1307 39.1763 23.1764 44.4956C23.6023 45.0567 24.2839 45.3902 24.9979 45.3902C25.7104 45.3902 26.3935 45.0567 26.8209 44.4956C30.8652 39.1763 40.2742 26.1037 40.2742 19.6135ZM25.2389 42.2246L24.9979 42.5448L24.757 42.2246C17.1197 32.0766 12.3718 23.4116 12.3718 19.6135C12.3718 12.7745 18.0349 7.21054 24.9979 7.21054C31.9597 7.21054 37.6226 12.7745 37.6226 19.6135C37.6241 23.4131 32.8792 32.0766 25.2389 42.2246Z" fill="#C4C4C4" >
            </path>




        </svg>
    );
}