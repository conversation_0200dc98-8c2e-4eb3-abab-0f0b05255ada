import React from 'react';
import { cn } from '@/lib/utils';
import { AgentIcon } from "@/components/ui/agent-icon";
import { RobotIcon } from "@/components/ui/robot-icon";

import { UserIcon } from "@/components/ui/user-icon";
import { LocationIcon } from "@/components/ui/location-icon";
interface AgentRowProps {
  avatarType: 'red' | 'blue' | 'orange' | 'gray';
  companyName: string;
  locationType: number;
  location: string;
  robotCount: string;
  actionButtons: Array<{
    label: string;
    color: 'blue' | 'green' | 'red' | 'orange';
    onClick?: () => void;
    disabled?: boolean;
    loading?: boolean;
  }>;
}

/**
 * 代理商信息行组件
 * 用于展示代理商的基本信息和操作按钮
 */
export const AgentRow: React.FC<AgentRowProps> = ({
  avatarType,
  companyName,
  locationType,
  location,

  robotCount,
  actionButtons
}) => {


  // 根据颜色获取按钮样式
  const getButtonClass = (color: string) => {
    switch (color) {
      case 'blue':
        return 'bg-blue-600 hover:bg-blue-700 text-white';
      case 'green':
        return 'bg-green-600 hover:bg-green-700 text-white';
      case 'red':
        return 'bg-red-600 hover:bg-red-700 text-white';
      case 'orange':
        return 'text-white';
      default:
        return 'bg-gray-600 hover:bg-gray-700 text-white';
    }
  };

  // 根据按钮标签获取特殊样式
  const getButtonStyle = (label: string, color: string) => {
    if (label === '补全信息' || label === '编辑归属') {
      return { background: "rgba(255, 141, 26, 1)" };
    }
    return {};
  };

  return (
    <div className="rounded-lg p-3 mb-2 flex justify-between" style={{ background: "linear-gradient(90deg, rgba(29, 31, 31, 1) 0%, rgba(53, 56, 59, 1) 26.11%, rgba(29, 30, 31, 1) 100%)" }}>

      <div className="flex items-center space-x-8 border-b border-green-500 pb-1 mb-1 w-3/6 mr-10">
        <AgentIcon color={avatarType} className="size-8" />
        <div className="text-white font-medium">
          {companyName}
        </div>

      </div>

      <div className="flex items-center space-x-8 border-b border-green-500 pb-1 mb-1 w-2/6 mr-10 items-end">

        {locationType === 1 ? (<LocationIcon color={avatarType} className="size-8" />) : (<UserIcon color={avatarType} className="size-8" />)}
        <span>{location} {locationType === 1}</span>
      </div>


      <div className="text-white flex items-center flex items-center space-x-8 border-b border-green-500 pb-1 mb-1 w-1/6 mr-10">
        <RobotIcon color={avatarType} className="size-8" />
        <span>{robotCount}</span>
      </div>

      <div className="flex items-center space-x-8 flex-shrink-0 min-w-max">

        <div className="flex space-x-2">
          {actionButtons.map((button, index) => (
            <button
              key={index}
              className={cn(
                "px-2 py-0.5 rounded text-xs font-medium transition-colors rounded-full",
                getButtonClass(button.color),
                (button.disabled || button.loading) && "opacity-50 cursor-not-allowed"
              )}
              style={getButtonStyle(button.label, button.color)}
              onClick={button.onClick}
              disabled={button.disabled || button.loading}
            >
              {button.loading ? `${button.label.includes('授权') ? '授权' : '关闭'}中...` : button.label}
            </button>
          ))}
        </div>

      </div>
    </div>
  );
};