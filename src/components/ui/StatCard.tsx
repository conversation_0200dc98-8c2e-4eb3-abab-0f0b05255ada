import React from 'react';
import { cn } from '@/lib/utils';
interface StatCardProps {
  title: string;
  value: string;
  iconUrl: string;
}

/**
 * 数据统计卡片组件
 * 用于展示关键业务指标的卡片，包含标题、数值和图标
 */
export const StatCard: React.FC<StatCardProps> = ({ title, value, iconUrl }) => {
  return (
    <div className=" bg-gray-800   rounded-lg shadow-md p-5 flex items-center justify-between min-w-[280px] flex-shrink-0"
      style={{
        background: 'linear-gradient(135deg, #202123ff 0%, #3f4042ff 100%)',
        borderRadius: '12px',
        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.08)',
        padding: '24px',
        width: '280px',
        textAlign: 'center',
        borderLeft: '4px solid #057a18ff'
      }}
    >
      <div className="flex flex-col">
        <h3 className="text-gray-300 text-[14px] font-normal">{title}</h3>
        <p className="text-white text-[20px] font-bold mt-1">{value}</p>
      </div>
      <div
        className="w-[50px] h-[50px] bg-contain bg-center bg-no-repeat flex-shrink-0"
        aria-label="统计图标"
      >
        <img
          src={iconUrl}
          alt="图标"
          className="w-full h-full object-contain"
        />
        {/* {iconType == 1 ? (
         "/images/robot_icon.svg"
        ) : iconType == 2 ? (
          <img
            src="/images/up_icon.svg"
            alt="图标"
            className="w-full h-full object-contain"
          />
        ) : iconType == 3 ? (
          <img
            src="/images/cicle_icon.svg"
            alt="图标"
            className="w-full h-full object-contain"
          />
        ) : null} */}

      </div>
    </div>
  );
};
