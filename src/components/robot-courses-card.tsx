"use client";

import { <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "lucide-react";
import { AgentIcon } from "@/components/ui/agent-icon";
import { <PERSON>, CardContent, CardHeader, CardFooter } from "@/components/ui/card";
import { RobotIcon } from "@/components/ui/robot-icon";
import { CompanyEditDialog } from "@/components/company-edit-dialog";
import { cn } from "@/lib/utils";
import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";

import { ApiClientError } from "@/services/api";
import { toastService } from "@/services/toast";
import { courseService } from "@/services/course";
import { agentService } from "@/services/agent";
import { CourseGrade } from "@/types/api";

interface Course {
  id: string;
  name: string;
  authorized: boolean;
}

interface RobotCourses {
  sn: string;
  id: string;
  company: string;
  location: string;
  totalRobots: number; // 机器人台数
  courses: Course[];
  isLocked?: boolean;
}

interface RobotCoursesCardProps {
  agentData: RobotCourses;
  // 授权所需的参数
  province?: string;
  city?: string;
  area?: string;
  agentId?: string;
  stageType?: 'UNKNOWN' | 'PRESCHOOL' | 'PRIMARY';
}

export function RobotCoursesCard({
  agentData,
  province,
  city,
  area,
  agentId,
  stageType = 'UNKNOWN'
}: RobotCoursesCardProps) {
  const router = useRouter();

  // 课程数据状态管理
  const [courses, setCourses] = useState<Course[]>(agentData.courses || []);
  const [loadingCourses, setLoadingCourses] = useState(false);

  // 课程授权状态管理
  const [authorizingCourses, setAuthorizingCourses] = useState<Record<string, boolean>>({});
  const [batchAuthorizing, setBatchAuthorizing] = useState(false);

  // 公司信息弹窗状态管理
  const [isCompanyDialogOpen, setIsCompanyDialogOpen] = useState(false);

  // 将 CourseGrade 转换为组件所需格式的辅助函数
  const convertCourseGradeToCourse = (courseGrade: CourseGrade): Course => {
    return {
      id: courseGrade.id.toString(),
      name: courseGrade.gradeName,
      authorized: courseGrade.status === 1 // 假设 status 1 表示已授权
    };
  };

  // 获取课程数据
  useEffect(() => {
    const loadCourses = async () => {
      // 如果没有必要的参数，跳过加载
      if (!province || !city || !agentId) {
        console.log('Missing required parameters for loading courses:', { province, city, agentId });
        return;
      }

      try {
        setLoadingCourses(true);

        // 构建API请求参数
        const searchReq = {
          province: province || undefined,
          city: city || undefined,
          area: area || undefined,
          agenId: agentId ? parseInt(agentId) : undefined, // API文档中的字段名
          stageType: stageType
        };

        console.log('Loading courses with params:', searchReq);

        // 调用API获取代理商课程
        const courseGrades = await agentService.getAgentCourses(searchReq);

        // 转换数据格式
        const convertedCourses = courseGrades.map(convertCourseGradeToCourse);
        setCourses(convertedCourses);

        console.log('Loaded courses:', convertedCourses);

      } catch (error) {
        const errorMessage = error instanceof ApiClientError
          ? error.message
          : '加载课程数据失败';
        toastService.error(errorMessage);
        console.error('Failed to load courses:', error);
      } finally {
        setLoadingCourses(false);
      }
    };

    loadCourses();
  }, [province, city, area, agentId, stageType]);

  // 处理单个课程授权
  const handleCourseAuthorization = async (courseId: string, enable: boolean) => {
    if (!province || !city || !agentId) {
      toastService.error('缺少必要的授权参数'+province +city +agentId);
      return;
    }

    try {
      setAuthorizingCourses(prev => ({ ...prev, [courseId]: true }));

      const params = {
        province,
        city,
        area,
        agenId: agentId ? parseInt(agentId) : undefined, // API文档中的字段名
        stageType,
        courseId: parseInt(courseId)
      };

      if (enable) {
        await courseService.enableAgentCourse(params);
        toastService.success('课程授权成功');
      } else {
        await courseService.disableAgentCourse(params);
        toastService.success('课程关闭成功');
      }

      // 这里可以触发数据刷新或更新本地状态
      // 实际项目中可能需要重新获取课程列表或更新父组件状态

    } catch (error) {
      const errorMessage = error instanceof ApiClientError
        ? error.message
        : `课程${enable ? '授权' : '关闭'}失败`;
      toastService.error(errorMessage);
      console.error(`Failed to ${enable ? 'enable' : 'disable'} course ${courseId}:`, error);
    } finally {
      setAuthorizingCourses(prev => ({ ...prev, [courseId]: false }));
    }
  };

  // 处理一键授权/关闭
  const handleBatchAuthorization = async (enable: boolean) => {
    if (!province || !city || !agentId) {
      toastService.error('缺少必要的授权参数');
      return;
    }

    try {
      setBatchAuthorizing(true);

      const params = {
        province,
        city,
        area,
        agenId: agentId ? parseInt(agentId) : undefined, // API文档中的字段名
        stageType
        // 注意：一键操作不传 courseId，表示对所有课程进行操作
      };

      if (enable) {
        await courseService.enableAgentCourse(params);
        toastService.success('一键授权成功');
      } else {
        await courseService.disableAgentCourse(params);
        toastService.success('一键关闭成功');
      }

      // 这里可以触发数据刷新

    } catch (error) {
      const errorMessage = error instanceof ApiClientError
        ? error.message
        : `一键${enable ? '授权' : '关闭'}失败`;
      toastService.error(errorMessage);
      console.error(`Failed to batch ${enable ? 'enable' : 'disable'} courses:`, error);
    } finally {
      setBatchAuthorizing(false);
    }
  };

  // 处理点击机器人台数，跳转到机器人详情页面
  const handleRobotCountClick = () => {
    // 构建跳转参数
    const params = new URLSearchParams();

    // 传递代理商信息
    if (province) params.set('agentProvince', province);
    if (city) params.set('agentCity', city);
    if (area) params.set('agentArea', area);
    if (agentId) params.set('agentId', agentId);
    if (agentData.company) params.set('agentName', agentData.company);
    if (agentData.totalRobots) params.set('totalRobots', agentData.totalRobots.toString());
    if (stageType) params.set('stageType', stageType);

    // 跳转到机器人详情页面
    router.push(`/courseware/robot/detail?${params.toString()}`);
  };



  return (
    <Card className="overflow-hidden border-muted bg-card text-card-foreground shadow-sm">
      <CardHeader className="pb-2">
        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
          <div className="flex items-center space-x-4">
            
            <div className="space-y-4">
              {/* 所有信息和操作按钮放在同一行 */}
              <div className="flex items-center justify-between w-full">
                {/* 左侧信息区域 */}
                <div className="flex items-center gap-8">
                  {/* 公司信息 */}
                  <div className="relative flex-shrink-0">
                    <div 
                      className={cn(
                        "flex items-center gap-2 h-8 px-2",
                        !agentData.company && "cursor-pointer hover:bg-white/10 rounded transition-colors"
                      )}
                      onClick={() => !agentData.company && setIsCompanyDialogOpen(true)}
                    >
                      <AgentIcon 
                        color={agentData.company ? "white" : "#ef4444"}
                        className="size-6"
                      />
                      <span className="text-sm text-white">
                        {agentData.company || "<暂无归属代理商>"}
                      </span>
                    </div>
                    <div className="absolute bottom-0 left-2 right-2 h-px bg-green-500" />
                  </div>
                  
                  {/* 位置信息 */}
                  <div className="relative flex-shrink-0">
                    <div className="flex items-center gap-2 h-8 px-2">
                      <MapPin className="size-6 text-white" />
                      <span className="text-sm text-white">{agentData.location}</span>
                    </div>
                    <div className="absolute bottom-0 left-2 right-2 h-px bg-green-500" />
                  </div>
                  
                  {/* 机器人数量 */}
                  <div className="relative flex-shrink-0">
                    <div
                      className="flex items-center gap-2 h-8 px-2 cursor-pointer hover:bg-white/10 rounded transition-colors"
                      onClick={handleRobotCountClick}
                    >
                      <RobotIcon color="white" className="size-6" />
                      <span className="text-sm text-white">{agentData.totalRobots}台</span>
                    </div>
                    <div className="absolute bottom-0 left-2 right-2 h-px bg-green-500" />
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          {/* 右侧操作按钮 - 完全居右显示 */}
          <div className="flex items-center gap-2">
            <button
              onClick={() => handleBatchAuthorization(true)}
              disabled={batchAuthorizing}
              className={cn(
                "inline-flex items-center justify-center rounded-full text-white text-xs px-3 py-0.5 h-6 font-medium transition-colors",
                batchAuthorizing
                  ? "bg-gray-500 cursor-not-allowed"
                  : "bg-green-600 hover:bg-green-700"
              )}
            >
              {batchAuthorizing ? "授权中..." : "一键授权"}
            </button>
            <button
              onClick={() => handleBatchAuthorization(false)}
              disabled={batchAuthorizing}
              className={cn(
                "inline-flex items-center justify-center rounded-full text-white text-xs px-3 py-0.5 h-6 font-medium transition-colors",
                batchAuthorizing
                  ? "bg-gray-500 cursor-not-allowed"
                  : "bg-red-600 hover:bg-red-700"
              )}
            >
              {batchAuthorizing ? "关闭中..." : "一键关闭"}
            </button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="pt-4">
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
          {loadingCourses ? (
            <div className="col-span-full text-center py-8 text-muted-foreground">
              加载课程中...
            </div>
          ) : courses.length === 0 ? (
            <div className="col-span-full text-center py-8 text-muted-foreground">
              暂无课程数据
            </div>
          ) : (
            courses.map((course) => (
            <Card key={course.id} className={cn(
              "transition-colors border-none",
              course.authorized ? "bg-card/80" : "bg-card/50"
            )}>
              <div className="relative">
                <CardContent className="px-24 py-1 flex justify-between items-center gap-2">
                  <span className="font-medium">{course.name}</span>
                  <div className="flex items-center gap-2">
                    <button
                      onClick={() => handleCourseAuthorization(course.id, true)}
                      disabled={authorizingCourses[course.id] || batchAuthorizing}
                      className={cn(
                        "inline-flex items-center justify-center rounded-full text-white text-xs px-3 py-0.5 h-6 font-medium transition-colors",
                        (authorizingCourses[course.id] || batchAuthorizing)
                          ? "bg-gray-500 cursor-not-allowed"
                          : "bg-green-600 hover:bg-green-700"
                      )}
                    >
                      {authorizingCourses[course.id] ? "授权中..." : "授权"}
                    </button>
                    <button
                      onClick={() => handleCourseAuthorization(course.id, false)}
                      disabled={authorizingCourses[course.id] || batchAuthorizing}
                      className={cn(
                        "inline-flex items-center justify-center rounded-full text-white text-xs px-3 py-0.5 h-6 font-medium transition-colors",
                        (authorizingCourses[course.id] || batchAuthorizing)
                          ? "bg-gray-500 cursor-not-allowed"
                          : "bg-red-600 hover:bg-red-700"
                      )}
                    >
                      {authorizingCourses[course.id] ? "关闭中..." : "关闭"}
                    </button>
                  </div>
                </CardContent>
                <div className="absolute bottom-0 left-20 right-20 h-px bg-green-500" />
              </div>
            </Card>
          )))}
        </div>
      </CardContent>
      
      <CardFooter className="flex justify-end pt-2 pb-4">
        <div className="text-xs text-muted-foreground flex items-center gap-1">
          <Bot className="size-3" />
          <span>共 {courses.length} 个课程</span>
        </div>
      </CardFooter>
      
      {/* 公司信息编辑弹窗 */}
      <CompanyEditDialog
        open={isCompanyDialogOpen}
        onOpenChange={setIsCompanyDialogOpen}
        currentCompany={agentData.company}
        onConfirm={(agentId, agentName) => {
          // 这里可以添加保存公司信息的逻辑
          console.log("保存公司信息:", { agentId, agentName });
          toastService.success(`已将代理商归属更新为: ${agentName}`);
        }}
        stageType="PRIMARY"
      />
    </Card>
  );
}
