"use client";

import { useState } from "react";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { TablePagination } from "@/components/ui/table-pagination";
import { cn } from "@/lib/utils";
import { Check } from "lucide-react";

interface Robot {
  id: string;
  sn: string;
  location: string;
  lastContact: string;
  selected?: boolean;
}

interface RobotsTableProps {
  robots: Robot[];
  totalRobots: number;
  onSelectChange?: (robotId: string, selected: boolean) => void;
  onSelectAll?: (selected: boolean) => void;
  pageSize?: number;
}

export function RobotsTable({ 
  robots,
  totalRobots,
  onSelectChange,
  onSelectAll,
  pageSize = 10
}: RobotsTableProps) {
  // 分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const totalPages = Math.ceil(totalRobots / pageSize);
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const currentRobots = robots.slice(startIndex, endIndex);
  
  // 选择状态
  const [selectedRobots, setSelectedRobots] = useState<Record<string, boolean>>(
    robots.reduce((acc, robot) => ({ ...acc, [robot.id]: robot.selected || false }), {})
  );
  
  // 全选状态
  const [selectAll, setSelectAll] = useState(false);
  
  // 处理单个选择变化
  const handleSelectChange = (robotId: string) => {
    const newValue = !selectedRobots[robotId];
    setSelectedRobots(prev => ({ ...prev, [robotId]: newValue }));
    if (onSelectChange) {
      onSelectChange(robotId, newValue);
    }
  };
  
  // 处理全选变化
  const handleSelectAll = () => {
    const newValue = !selectAll;
    setSelectAll(newValue);
    
    const newSelectedRobots = { ...selectedRobots };
    currentRobots.forEach(robot => {
      newSelectedRobots[robot.id] = newValue;
    });
    
    setSelectedRobots(newSelectedRobots);
    
    if (onSelectAll) {
      onSelectAll(newValue);
    }
  };
  
  return (
    <div className="w-full rounded-lg overflow-hidden" style={{ background: 'rgba(16, 15, 15, 0.98)', backdropFilter: 'blur(50px)' }}>
      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow className="border-b border-white/20 hover:bg-white/5">
              <TableHead className="font-medium text-gray-400 px-6 py-2 w-16">
                <div 
                  className={cn(
                    "w-5 h-5 border border-gray-500 rounded flex items-center justify-center cursor-pointer",
                    selectAll && "bg-green-600 border-green-600"
                  )}
                  onClick={handleSelectAll}
                >
                  {selectAll && <Check className="h-4 w-4 text-white" />}
                </div>
              </TableHead>
              <TableHead className="font-medium text-gray-400 px-6 py-2">机器人ID</TableHead>
              <TableHead className="font-medium text-gray-400 px-6 py-2">机器人SN</TableHead>
              <TableHead className="font-medium text-gray-400 px-6 py-2">机器人最后获取位置</TableHead>
              <TableHead className="font-medium text-gray-400 px-6 py-2">机器人最后联网时间</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {currentRobots.map((robot: Robot) => (
              <TableRow 
                key={robot.id} 
                className="border-b border-white/10 hover:bg-white/5"
              >
                <TableCell className="px-6 py-2">
                  <div 
                    className={cn(
                      "w-5 h-5 border border-gray-500 rounded flex items-center justify-center cursor-pointer",
                      selectedRobots[robot.id] && "bg-green-600 border-green-600"
                    )}
                    onClick={() => handleSelectChange(robot.id)}
                  >
                    {selectedRobots[robot.id] && <Check className="h-4 w-4 text-white" />}
                  </div>
                </TableCell>
                <TableCell className="px-6 py-2 font-mono text-sm text-white">
                  {robot.id}
                </TableCell>
                <TableCell className="px-6 py-2 font-mono text-sm text-white/80">
                  {robot.sn}
                </TableCell>
                <TableCell className="px-6 py-2 text-sm text-white/80">
                  {robot.location}
                </TableCell>
                <TableCell className="px-6 py-2 text-sm text-white/80">
                  {robot.lastContact}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
      {/* 使用公共分页组件 */}
      <TablePagination
        currentPage={currentPage}
        totalPages={totalPages}
        totalItems={totalRobots}
        pageSize={pageSize}
        onPageChange={setCurrentPage}
      />
    </div>
  );
}