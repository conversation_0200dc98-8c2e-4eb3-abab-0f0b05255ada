"use client";

import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>en } from "lucide-react";
import { AgentIcon } from "@/components/ui/agent-icon";
import { <PERSON>, CardContent, CardHeader, CardFooter } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { CustomInput } from "@/components/ui/custom-input";
import { CompanyEditDialog } from "@/components/company-edit-dialog";
import { cn } from "@/lib/utils";
import { useState } from "react";
import { snService } from "@/services/sn";
import { ApiClientError } from "@/services/api";
import { toastService } from "@/services/toast";

interface Course {
  id: string;
  name: string;
  authorized: boolean;
}

interface RobotInfo {
  sn: string;
  id: string;
  company: string;
  location: string;
  courses: Course[];
  isLocked?: boolean;
}

interface RobotInfoCardProps {
  robotInfo: RobotInfo;
}

export function RobotIn<PERSON>Card({ robotInfo }: RobotInfoCardProps) {
  // 获取机器人ID的首字母作为头像备用显示
  const robotInitial = robotInfo.id.charAt(0).toUpperCase();
  
  // SN输入状态管理
  const [snValue, setSnValue] = useState(robotInfo.sn || "");
  const [isEditingSn, setIsEditingSn] = useState(!robotInfo.sn);

  // API调用状态管理
  const [isSubmittingSn, setIsSubmittingSn] = useState(false);
  const [snSuccess, setSnSuccess] = useState(false);

  // 公司信息弹窗状态管理
  const [isCompanyDialogOpen, setIsCompanyDialogOpen] = useState(false);

  // 锁机状态管理
  const [isLocked, setIsLocked] = useState(robotInfo.isLocked || false);
  const [isLockingUnlocking, setIsLockingUnlocking] = useState(false);

  // 处理SN输入变化
  const handleSnChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSnValue(e.target.value);
    // 输入变化时清除成功状态
    setSnSuccess(false);
  };

  // 处理SN确认提交
  const handleSnConfirm = async () => {
    if (!snValue.trim()) {
      toastService.error("请输入有效的SN码");
      return;
    }

    try {
      setIsSubmittingSn(true);

      // 调用 API 补充 SN
      await snService.supplementSn(robotInfo.id, snValue.trim());

      // API 调用成功
      console.log(`Successfully supplemented SN for robot ${robotInfo.id}: ${snValue}`);
      setSnSuccess(true);
      setIsEditingSn(false);
      toastService.success(`机器人 ${robotInfo.id} 的SN码已成功更新`);

    } catch (error) {
      // 处理错误
      const errorMessage = error instanceof ApiClientError
        ? error.message
        : '修改序列号失败，请重试';

      console.error(`Failed to supplement SN for robot ${robotInfo.id}:`, error);
      // 使用toast显示错误，而不是设置本地错误状态
      toastService.error(errorMessage);

    } finally {
      // 清除提交状态
      setIsSubmittingSn(false);
    }
  };

  // 处理锁机/解锁切换
  const handleLockToggle = async (checked: boolean) => {
    const currentSn = snValue || robotInfo.sn;
    if (!currentSn) {
      toastService.error("请先输入SN码");
      return;
    }

    try {
      setIsLockingUnlocking(true);

      if (checked) {
        // 锁机
        await snService.lockSn(currentSn);
        console.log(`Successfully locked robot with SN: ${currentSn}`);
        toastService.success(`机器人已成功锁机`);
      } else {
        // 解锁
        await snService.unlockSn(currentSn);
        console.log(`Successfully unlocked robot with SN: ${currentSn}`);
        toastService.success(`机器人已成功解锁`);
      }

      // 成功后更新状态
      setIsLocked(checked);

    } catch (error) {
      // 处理错误
      const errorMessage = error instanceof ApiClientError
        ? error.message
        : (checked ? '锁机失败，请重试' : '解锁失败，请重试');

      console.error(`Failed to ${checked ? 'lock' : 'unlock'} robot with SN ${currentSn}:`, error);
      toastService.error(errorMessage);

    } finally {
      setIsLockingUnlocking(false);
    }
  };

  return (
    <Card className="overflow-hidden border-muted bg-card text-card-foreground shadow-sm">
      <CardHeader className="pb-2">
        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-24 border-primary/20 overflow-hidden">
              <img 
                src="/images/robot.m.png" 
                alt={`机器人 ${robotInfo.id}`}
                className="w-full h-full object-cover"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.style.display = 'none';
                  const fallback = target.nextElementSibling as HTMLElement;
                  if (fallback) fallback.style.display = 'flex';
                }}
              />
              <div 
                className="w-full h-full bg-primary/10 text-primary text-lg font-bold flex items-center justify-center"
                style={{ display: 'none' }}
              >
                {robotInitial}
              </div>
            </div>
            
            <div className="space-y-4">
              {/* 第一行：SN、ID、公司、位置 */}
              <div className="flex items-end justify-between gap-4 w-full min-w-0">
                {/* SN 整体（图标+值） */}
                <div className="relative flex-shrink-0">
                  <div className="flex items-center gap-2 h-8 px-2">
                    <div className="relative inline-flex items-center justify-center">
                      <Scan className="size-6 text-muted-foreground/70" />
                      <span className="absolute inset-0 flex items-center justify-center text-xs font-bold text-foreground">
                        SN
                      </span>
                    </div>
                    {isEditingSn ? (
                      <div className="relative">
                        <CustomInput
                          value={snValue}
                          onChange={handleSnChange}
                          placeholder="请输入SN码"
                          className="font-mono text-sm h-6 px-2 py-1 min-w-[120px] pr-8"
                          autoFocus
                          disabled={isSubmittingSn}
                        />
                        <button
                          className={cn(
                            "absolute right-1 top-1/2 -translate-y-1/2 h-6 w-6 flex items-center justify-center transition-colors",
                            isSubmittingSn
                              ? "text-gray-400 cursor-not-allowed"
                              : "text-green-400 hover:text-green-300"
                          )}
                          onClick={handleSnConfirm}
                          disabled={isSubmittingSn}
                        >
                          <Check className="h-4 w-4" />
                          <span className="sr-only">
                            {isSubmittingSn ? "提交中..." : "确认"}
                          </span>
                        </button>
                      </div>
                    ) : (
                      <div className="flex items-center gap-1">
                        <span className={cn(
                          "font-mono text-sm",
                          snSuccess && "text-green-400"
                        )}>
                          {snValue || robotInfo.sn}
                        </span>
                        {snSuccess && (
                          <Check className="h-4 w-4 text-green-400" />
                        )}
                        {(robotInfo.sn || snValue) && (
                          <button
                            className="text-muted-foreground hover:text-white transition-colors"
                            onClick={() => {
                              setIsEditingSn(true);
                              setSnSuccess(false);
                            }}
                          >
                            <SquarePen className="h-4 w-4" />
                            <span className="sr-only">修改SN</span>
                          </button>
                        )}
                      </div>
                    )}
                  </div>
                  <div className="absolute bottom-0 left-2 right-2 h-px bg-green-500" />
                </div>
                
                {/* ID 整体（图标+值） */}
                <div className="relative flex-shrink-0">
                  <div className="flex items-center gap-2 h-8 px-2">
                    <div className="relative inline-flex items-center justify-center">
                      <Scan className="size-6 text-muted-foreground/70" />
                      <span className="absolute inset-0 flex items-center justify-center text-xs font-bold text-foreground">
                        ID
                      </span>
                    </div>
                    <span className="font-mono text-sm">{robotInfo.id}</span>
                  </div>
                  <div className="absolute bottom-0 left-2 right-2 h-px bg-green-500" />
                </div>
                
                {/* User 信息整体 */}
                <div className="relative flex-shrink-0">
                  <div 
                    className={cn(
                      "flex items-center gap-2 h-8 px-2",
                      !robotInfo.company && "cursor-pointer hover:bg-white/10 rounded transition-colors"
                    )}
                    onClick={() => !robotInfo.company && setIsCompanyDialogOpen(true)}
                  >
                    <AgentIcon 
                      color={robotInfo.company ? "white" : "#ef4444"}
                      className="size-6"
                    />
                    <span className="text-sm text-white">
                      {robotInfo.company || "<暂无信息>"}
                    </span>
                  </div>
                  <div className="absolute bottom-0 left-2 right-2 h-px bg-green-500" />
                </div>
                
                {/* MapPin 信息整体 */}
                <div className="relative flex-shrink-0">
                  <div className="flex items-center gap-2 h-8 px-2">
                    <MapPin className="size-6 text-white" />
                    <span className="text-sm text-white">{robotInfo.location}</span>
                  </div>
                  <div className="absolute bottom-0 left-2 right-2 h-px bg-green-500" />
                </div>
              </div>
              
              {/* 第二行：锁机和课程授权单独调整 */}
              <div className="flex flex-wrap items-center gap-12">
                {/* 锁机开关 - 仅当SN确认提交成功后显示 */}
                {(robotInfo.sn || (snValue && !isEditingSn)) && (
                  <div className="flex flex-col items-start ml-4">
                    <Switch 
                      checked={isLocked}
                      onCheckedChange={handleLockToggle}
                      checkedText="已锁机"
                      uncheckedText="已解锁"
                      disabled={isLockingUnlocking}
                      className={cn(
                        "data-[state=checked]:bg-red-600 data-[state=unchecked]:bg-green-600"
                      )}
                    />
                  </div>
                )}
                
                {/* 课程授权单独调整 - 居中显示 */}
                <div className="flex-1 flex justify-center">
                  <div className="relative">
                    <div className="flex items-center gap-2 text-white px-4 py-2 rounded-lg">
                      <BookOpen className="size-4" />
                      <span className="text-sm font-medium">课程授权单独调整</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="pt-4">
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
          {robotInfo.courses.map((course) => (
            <Card key={course.id} className={cn(
              "transition-colors border-none",
              course.authorized ? "bg-card/80" : "bg-card/50"
            )}>
              <div className="relative">
                <CardContent className="px-24 py-1 flex justify-between items-center gap-2">
                  <span className="font-medium">{course.name}</span>
                  <Badge 
                    variant={course.authorized ? "default" : "destructive"}
                    className={cn(
                      "transition-colors flex-shrink-0",
                      course.authorized 
                        ? "bg-green-600/90 hover:bg-green-600 text-white" 
                        : "bg-destructive/90 hover:bg-destructive text-destructive-foreground"
                    )}
                  >
                    {course.authorized ? "已授权" : "未授权"}
                  </Badge>
                </CardContent>
                <div className="absolute bottom-0 left-20 right-20 h-px bg-green-500" />
              </div>
            </Card>
          ))}
        </div>
      </CardContent>
      
      <CardFooter className="flex justify-end pt-2 pb-4">
        <div className="text-xs text-muted-foreground flex items-center gap-1">
          <Bot className="size-3" />
          <span>共 {robotInfo.courses.length} 个课程</span>
        </div>
      </CardFooter>
      
      {/* 公司信息编辑弹窗 */}
      <CompanyEditDialog
        open={isCompanyDialogOpen}
        onOpenChange={setIsCompanyDialogOpen}
        currentCompany={robotInfo.company}
        onConfirm={(agentId, agentName) => {
          // 这里可以添加保存公司信息的逻辑
          console.log("保存公司信息:", { agentId, agentName });
          toastService.success(`已将机器人归属更新为: ${agentName}`);
        }}
        stageType="PRIMARY"
      />
    </Card>
  );
}
