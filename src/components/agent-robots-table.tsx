"use client";

import { <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "lucide-react";
import { AgentIcon } from "@/components/ui/agent-icon";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { OrderSnLocation } from "@/types/api";

interface AgentRobotsTableProps {
  agentName: string;
  location: string;
  robots: OrderSnLocation[];
}

export function AgentRobotsTable({ agentName, location, robots }: AgentRobotsTableProps) {
  return (
    <Card className="overflow-hidden border-muted bg-card text-card-foreground shadow-sm">
      <CardHeader className="pb-2">
        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
          <div className="flex items-center space-x-4">
            <div className="space-y-4">
              {/* 代理商信息和机器人总数 */}
              <div className="flex items-center justify-between w-full">
                {/* 左侧信息区域 */}
                <div className="flex items-center gap-8">
                  {/* 公司信息 */}
                  <div className="relative flex-shrink-0">
                    <div className="flex items-center gap-2 h-8 px-2">
                      <AgentIcon color="white" className="size-6" />
                      <span className="text-sm text-white">{agentName}</span>
                    </div>
                    <div className="absolute bottom-0 left-2 right-2 h-px bg-green-500" />
                  </div>
                  
                  {/* 位置信息 */}
                  <div className="relative flex-shrink-0">
                    <div className="flex items-center gap-2 h-8 px-2">
                      <MapPin className="size-6 text-white" />
                      <span className="text-sm text-white">{location}</span>
                    </div>
                    <div className="absolute bottom-0 left-2 right-2 h-px bg-green-500" />
                  </div>
                  
                  {/* 机器人总数 */}
                  <div className="relative flex-shrink-0">
                    <div className="flex items-center gap-2 h-8 px-2">
                      <Bot className="size-6 text-white" />
                      <span className="text-sm text-white">{robots.length}台</span>
                    </div>
                    <div className="absolute bottom-0 left-2 right-2 h-px bg-green-500" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="pt-4">
        {robots.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            暂无机器人数据
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="border-b border-muted">
                  <th className="text-left py-3 px-4 font-medium text-muted-foreground">序号</th>
                  <th className="text-left py-3 px-4 font-medium text-muted-foreground">机器人ID</th>
                  <th className="text-left py-3 px-4 font-medium text-muted-foreground">SN</th>
                  <th className="text-left py-3 px-4 font-medium text-muted-foreground">省份</th>
                  <th className="text-left py-3 px-4 font-medium text-muted-foreground">城市</th>
                  <th className="text-left py-3 px-4 font-medium text-muted-foreground">区域</th>
                </tr>
              </thead>
              <tbody>
                {robots.map((robot, index) => (
                  <tr 
                    key={robot.id} 
                    className={cn(
                      "border-b border-muted/50 hover:bg-muted/20 transition-colors",
                      index % 2 === 0 ? "bg-card/50" : "bg-card/30"
                    )}
                  >
                    <td className="py-3 px-4 text-sm">{index + 1}</td>
                    <td className="py-3 px-4 text-sm font-mono">{robot.robotId}</td>
                    <td className="py-3 px-4 text-sm font-mono">{robot.sn || '-'}</td>
                    <td className="py-3 px-4 text-sm">{robot.province}</td>
                    <td className="py-3 px-4 text-sm">{robot.city}</td>
                    <td className="py-3 px-4 text-sm">{robot.area || '-'}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
