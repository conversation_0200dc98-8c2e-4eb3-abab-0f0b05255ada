"use client";

import { Monitor, LogOut, Settings, BookOpen, ArchiveRestore, BarChart3, Users, X, Home } from "lucide-react";

import Link from "next/link";
import { usePathname, useSearchParams } from "next/navigation";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { cn } from "@/lib/utils";

interface SidebarProps {
  isOpen?: boolean;
  onClose?: () => void;
}

export function Sidebar({ isOpen = true, onClose }: SidebarProps) {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  
  // 判断菜单项是否激活
  const isActive = (path: string, queryParam?: {key: string, value: string}) => {
    if (path === '/') {
      return pathname === path;
    }
    
    // 如果没有查询参数，只检查路径
    if (!queryParam) {
      return pathname.startsWith(path);
    }
    
    // 检查路径和查询参数 - 使用Next.js的useSearchParams
    const paramValue = searchParams.get(queryParam.key);
    return pathname.startsWith(path) && paramValue === queryParam.value;
  };
  return (
    <>
      {/* 桌面端侧边栏 */}
      <aside className="hidden lg:fixed lg:left-0 lg:top-16 lg:bottom-0 lg:z-50 lg:flex lg:w-64 lg:flex-col">
        <div className="flex grow flex-col gap-y-5 overflow-y-auto bg-black text-white px-6 pb-4 pt-6">
          
          <nav className="flex flex-1 flex-col">
            <ScrollArea className="flex-1">
              <div className="space-y-4">
                {/* 首页按钮 */}
                <Button 
                  asChild 
                  className={cn(
                    "w-full justify-start text-white rounded-full py-3 px-4",
                    isActive('/') 
                      ? "bg-gray-700" 
                      : "bg-transparent hover:bg-gray-800"
                  )}
                >
                  <Link href="/" className="flex items-center gap-3">
                    <Home className="size-5" style={{ color: isActive('/') ? 'rgba(110, 226, 87, 1)' : 'white' }} />
                    <span className="text-base" style={{ color: isActive('/') ? 'rgba(110, 226, 87, 1)' : 'white' }}>首页</span>
                  </Link>
                </Button>
                
                {/* 机器人课件管理分组 */}
                <div className="space-y-3 mt-8">
                  <h3 className="text-gray-400 text-sm font-medium px-2">机器人课件管理</h3>
                  
                  <div className="space-y-2">
                    <Button 
                      asChild 
                      variant="ghost" 
                      className={cn(
                        "w-full justify-start py-3",
                        isActive('/courseware/agent/list', {key: 'stageType', value: 'PRESCHOOL'})
                          ? "bg-gray-700 text-white rounded-full"
                          : "text-gray-300 hover:text-white hover:bg-gray-800"
                      )}
                    >
                      <Link href="/courseware/agent/list?stageType=PRESCHOOL" className="flex items-center gap-3">
                        <BookOpen className="size-5" style={{ 
                          color: isActive('/courseware/agent/list', {key: 'stageType', value: 'PRESCHOOL'})
                            ? 'rgba(110, 226, 87, 1)' 
                            : 'currentColor' 
                        }} />
                        <span className="text-base" style={{ 
                          color: isActive('/courseware/agent/list', {key: 'stageType', value: 'PRESCHOOL'})
                            ? 'rgba(110, 226, 87, 1)' 
                            : 'currentColor' 
                        }}>幼教课程授权</span>
                      </Link>
                    </Button>
                    
                    <Button 
                      asChild 
                      variant="ghost" 
                      className={cn(
                        "w-full justify-start py-3",
                        isActive('/courseware/upload')
                          ? "bg-gray-700 text-white rounded-full"
                          : "text-gray-300 hover:text-white hover:bg-gray-800"
                      )}
                    >
                      <Link href="/courseware/upload" className="flex items-center gap-3">
                        <ArchiveRestore className="size-5" style={{ 
                          color: isActive('/courseware/upload') 
                            ? 'rgba(110, 226, 87, 1)' 
                            : 'currentColor' 
                        }} />
                        <span className="text-base" style={{ 
                          color: isActive('/courseware/upload') 
                            ? 'rgba(110, 226, 87, 1)' 
                            : 'currentColor' 
                        }}>课件上传管理</span>
                      </Link>
                    </Button>
                    
                    <Button 
                      asChild 
                      variant="ghost" 
                      className={cn(
                        "w-full justify-start py-3",
                        isActive('/courseware/agent/list', {key: 'stageType', value: 'PRIMARY'})
                          ? "bg-gray-700 text-white rounded-full"
                          : "text-gray-300 hover:text-white hover:bg-gray-800"
                      )}
                    >
                      <Link href="/courseware/agent/list?stageType=PRIMARY" className="flex items-center gap-3">
                        <BookOpen className="size-5" style={{ 
                          color: isActive('/courseware/agent/list', {key: 'stageType', value: 'PRIMARY'})
                            ? 'rgba(110, 226, 87, 1)' 
                            : 'currentColor' 
                        }} />
                        <span className="text-base" style={{ 
                          color: isActive('/courseware/agent/list', {key: 'stageType', value: 'PRIMARY'})
                            ? 'rgba(110, 226, 87, 1)' 
                            : 'currentColor' 
                        }}>小学课程授权</span>
                      </Link>
                    </Button>
                    
                    <Button 
                      asChild 
                      variant="ghost" 
                      className={cn(
                        "w-full justify-start py-3",
                        isActive('/courseware/free')
                          ? "bg-gray-700 text-white rounded-full"
                          : "text-gray-300 hover:text-white hover:bg-gray-800"
                      )}
                    >
                      <Link href="/courseware/free" className="flex items-center gap-3">
                        <Settings className="size-5" style={{ 
                          color: isActive('/courseware/free') 
                            ? 'rgba(110, 226, 87, 1)' 
                            : 'currentColor' 
                        }} />
                        <span className="text-base" style={{ 
                          color: isActive('/courseware/free') 
                            ? 'rgba(110, 226, 87, 1)' 
                            : 'currentColor' 
                        }}>免费课程管理</span>
                      </Link>
                    </Button>
                  </div>
                </div>
              </div>
            </ScrollArea>
            
            {/* 底部退出按钮 */}
            <div className="mt-auto pt-4">
              <Button 
                variant="ghost" 
                className="w-full justify-start text-gray-300 hover:text-white hover:bg-gray-800 py-3"
              >
                <LogOut className="size-5 mr-3" />
                <span className="text-base">退出账号</span>
              </Button>
            </div>
          </nav>
        </div>
      </aside>

      {/* 移动端侧边栏 */}
      <div className={cn(
        "fixed left-0 top-16 bottom-0 z-50 flex w-64 flex-col transition-transform duration-300 ease-in-out lg:hidden",
        isOpen ? "translate-x-0" : "-translate-x-full"
      )}>
        <div className="flex grow flex-col gap-y-5 overflow-y-auto bg-black text-white px-6 pb-4">
          {/* 顶部标题和关闭按钮 */}
          <div className="flex h-16 shrink-0 items-center justify-end pt-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="size-8 p-0 hover:bg-gray-800 text-white"
            >
              <X className="size-4" />
            </Button>
          </div>
          
          <nav className="flex flex-1 flex-col">
            <ScrollArea className="flex-1">
              <div className="space-y-4">
                {/* 首页按钮 */}
                <Button 
                  asChild 
                  className={cn(
                    "w-full justify-start text-white rounded-full py-3 px-4",
                    isActive('/') 
                      ? "bg-gray-700" 
                      : "bg-transparent hover:bg-gray-800"
                  )}
                  onClick={onClose}
                >
                  <Link href="/" className="flex items-center gap-3">
                    <Home className="size-5" style={{ color: isActive('/') ? 'rgba(110, 226, 87, 1)' : 'white' }} />
                    <span className="text-base" style={{ color: isActive('/') ? 'rgba(110, 226, 87, 1)' : 'white' }}>首页</span>
                  </Link>
                </Button>
                
                {/* 机器人课件管理分组 */}
                <div className="space-y-3 mt-8">
                  <h3 className="text-gray-400 text-sm font-medium px-2">机器人课件管理</h3>
                  
                  <div className="space-y-2">
                    <Button 
                      asChild 
                      variant="ghost" 
                      className={cn(
                        "w-full justify-start py-3",
                        isActive('/courseware/agent/list', {key: 'stageType', value: 'PRESCHOOL'})
                          ? "bg-gray-700 text-white rounded-full"
                          : "text-gray-300 hover:text-white hover:bg-gray-800"
                      )}
                      onClick={onClose}
                    >
                      <Link href="/courseware/agent/list?stageType=PRESCHOOL" className="flex items-center gap-3">
                        <BookOpen className="size-5" style={{ 
                          color: isActive('/courseware/agent/list', {key: 'stageType', value: 'PRESCHOOL'}) 
                            ? 'rgba(110, 226, 87, 1)' 
                            : 'currentColor' 
                        }} />
                        <span className="text-base" style={{ 
                          color: isActive('/courseware/agent/list', {key: 'stageType', value: 'PRESCHOOL'}) 
                            ? 'rgba(110, 226, 87, 1)' 
                            : 'currentColor' 
                        }}>幼教课程授权</span>
                      </Link>
                    </Button>
                    
                    <Button 
                      asChild 
                      variant="ghost" 
                      className={cn(
                        "w-full justify-start py-3",
                        isActive('/courseware/upload')
                          ? "bg-gray-700 text-white rounded-full"
                          : "text-gray-300 hover:text-white hover:bg-gray-800"
                      )}
                      onClick={onClose}
                    >
                      <Link href="/courseware/upload" className="flex items-center gap-3">
                        <Monitor className="size-5" style={{ 
                          color: isActive('/courseware/upload') 
                            ? 'rgba(110, 226, 87, 1)' 
                            : 'currentColor' 
                        }} />
                        <span className="text-base" style={{ 
                          color: isActive('/courseware/upload') 
                            ? 'rgba(110, 226, 87, 1)' 
                            : 'currentColor' 
                        }}>课件上传管理</span>
                      </Link>
                    </Button>
                    
                    <Button 
                      asChild 
                      variant="ghost" 
                      className={cn(
                        "w-full justify-start py-3",
                        isActive('/courseware/agent/list', {key: 'stageType', value: 'PRIMARY'})
                          ? "bg-gray-700 text-white rounded-full"
                          : "text-gray-300 hover:text-white hover:bg-gray-800"
                      )}
                      onClick={onClose}
                    >
                      <Link href="/courseware/agent/list?stageType=PRIMARY" className="flex items-center gap-3">
                        <BookOpen className="size-5" style={{ 
                          color: isActive('/courseware/agent/list', {key: 'stageType', value: 'PRIMARY'}) 
                            ? 'rgba(110, 226, 87, 1)' 
                            : 'currentColor' 
                        }} />
                        <span className="text-base" style={{ 
                          color: isActive('/courseware/agent/list', {key: 'stageType', value: 'PRIMARY'}) 
                            ? 'rgba(110, 226, 87, 1)' 
                            : 'currentColor' 
                        }}>小学课程授权</span>
                      </Link>
                    </Button>
                    
                    <Button 
                      asChild 
                      variant="ghost" 
                      className={cn(
                        "w-full justify-start py-3",
                        isActive('/courseware/free')
                          ? "bg-gray-700 text-white rounded-full"
                          : "text-gray-300 hover:text-white hover:bg-gray-800"
                      )}
                      onClick={onClose}
                    >
                      <Link href="/courseware/free" className="flex items-center gap-3">
                        <Settings className="size-5" style={{ 
                          color: isActive('/courseware/free') 
                            ? 'rgba(110, 226, 87, 1)' 
                            : 'currentColor' 
                        }} />
                        <span className="text-base" style={{ 
                          color: isActive('/courseware/free') 
                            ? 'rgba(110, 226, 87, 1)' 
                            : 'currentColor' 
                        }}>免费课程管理</span>
                      </Link>
                    </Button>
                  </div>
                </div>
              </div>
            </ScrollArea>
            
            {/* 底部退出按钮 */}
            <div className="mt-auto pt-4">
              <Button 
                variant="ghost" 
                className="w-full justify-start text-gray-300 hover:text-white hover:bg-gray-800 py-3"
                onClick={onClose}
              >
                <LogOut className="size-5 mr-3" />
                <span className="text-base">退出账号</span>
              </Button>
            </div>
          </nav>
        </div>
      </div>
    </>
  );
}
