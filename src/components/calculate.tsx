import { StatCard } from '@/components/ui/StatCard';

interface CalculateInfo {
  id:number
  title: string;
  value: string ;
  iconUrl: string;
}

interface CalculateInfoProps {
  calculateInfo: CalculateInfo[];
}

export function Calculate({ calculateInfo }: CalculateInfoProps) {
  return (
    <div className="  text-white  min-h-screen">
      <div className="flex justify-between gap-6 max-w-6xl mx-auto">
        {calculateInfo.map((item) => (
          <StatCard
            key={item.id}
            title={item.title}
            value={item.value}
            iconUrl={item.iconUrl}
          />
        ))}
      </div>
    </div>
  );
}