"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { CustomInput } from "@/components/ui/custom-input";
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from "@/components/ui/pagination";
import { cn } from "@/lib/utils";

interface Robot {
  id: string;
  sn: string;
  location: string;
  lastContact: string;
}

interface RobotsListProps {
  robots: Robot[];
  totalRobots: number;
  onConfirm?: (robotId: string, sn: string) => void;
  onCancel?: (robotId: string) => void;
  pageSize?: number;
}

export function RobotsList({ 
  robots,
  totalRobots,
  onConfirm,
  onCancel,
  pageSize = 10
}: RobotsListProps) {
  // 分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const totalPages = Math.ceil(totalRobots / pageSize);
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const currentRobots = robots.slice(startIndex, endIndex);
  
  // 用于跟踪每行的SN输入值
  const [snInputs, setSnInputs] = useState<Record<string, string>>(
    robots.reduce((acc, robot) => ({ ...acc, [robot.id]: robot.sn || '' }), {})
  );
  
  // 用于跟踪哪一行正在进行二次确认
  const [confirmingRows, setConfirmingRows] = useState<Record<string, boolean>>(
    robots.reduce((acc, robot) => ({ ...acc, [robot.id]: false }), {})
  );
  
  // 处理SN输入变化
  const handleSnChange = (robotId: string, value: string) => {
    setSnInputs(prev => ({ ...prev, [robotId]: value }));
    // 如果正在二次确认状态，输入变化时取消二次确认
    if (confirmingRows[robotId]) {
      setConfirmingRows(prev => ({ ...prev, [robotId]: false }));
    }
  };
  
  // 处理首次确认操作
  const handleFirstConfirm = (robotId: string) => {
    const sn = snInputs[robotId];
    if (sn && sn.trim()) {
      // 如果SN已填写，启用二次确认
      setConfirmingRows(prev => ({ ...prev, [robotId]: true }));
    }
  };
  
  // 处理最终确认操作
  const handleFinalConfirm = (robotId: string) => {
    const sn = snInputs[robotId];
    if (onConfirm) {
      onConfirm(robotId, sn);
    }
    // 确认后取消二次确认状态
    setConfirmingRows(prev => ({ ...prev, [robotId]: false }));
  };
  
  // 处理取消操作
  const handleCancelConfirm = (robotId: string) => {
    // 取消二次确认状态
    setConfirmingRows(prev => ({ ...prev, [robotId]: false }));
    if (onCancel) {
      onCancel(robotId);
    }
  };
  
  return (
    <div className="w-full rounded-lg overflow-hidden" style={{ background: 'rgba(16, 15, 15, 0.98)', backdropFilter: 'blur(50px)' }}>
      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow className="border-b border-white/20 hover:bg-white/5">
              <TableHead className="font-medium text-gray-400 px-6 py-2">机器人ID</TableHead>
              <TableHead className="font-medium text-gray-400 px-6 py-2">机器人最后联网时间</TableHead>
              <TableHead className="font-medium text-gray-400 px-6 py-2">机器人最后获取位置</TableHead>
              <TableHead className="font-medium text-gray-400 px-6 py-2 min-w-[250px]">填写机器人SN</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {currentRobots.map((robot: Robot, index: number) => (
              <TableRow 
                key={robot.id} 
                className="border-b border-white/10 hover:bg-white/5"
              >
                <TableCell className="px-6 py-2 font-mono text-sm text-white">
                  {robot.id}
                </TableCell>
                <TableCell className="px-6 py-2 text-sm text-white/80">
                  {robot.lastContact}
                </TableCell>
                <TableCell className="px-6 py-2 text-sm text-white/80">
                  {robot.location}
                </TableCell>
                <TableCell className="px-6 py-2">
                  <div className="relative">
                    <CustomInput
                      value={snInputs[robot.id]}
                      onChange={(e) => handleSnChange(robot.id, e.target.value)}
                      placeholder="请输入SN码"
                      className="h-8 pr-16"
                    />
                    {!confirmingRows[robot.id] ? (
                      <Button 
                        size="sm" 
                        onClick={() => handleFirstConfirm(robot.id)}
                        className="absolute right-1 top-1/2 -translate-y-1/2 bg-green-600 hover:bg-green-500 text-white border-0 px-3 h-6 text-xs"
                      >
                        确认
                      </Button>
                    ) : (
                      <div className="absolute right-1 top-1/2 -translate-y-1/2 flex gap-1">
                        <Button 
                          size="sm" 
                          onClick={() => handleFinalConfirm(robot.id)}
                          className="bg-green-600 hover:bg-green-500 text-white border-0 px-1.5 h-6 text-xs"
                        >
                          确认
                        </Button>
                        <Button 
                          size="sm" 
                          onClick={() => handleCancelConfirm(robot.id)}
                          className="bg-red-600 hover:bg-red-500 text-white border-0 px-1.5 h-6 text-xs"
                        >
                          取消
                        </Button>
                      </div>
                    )}
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
      
      {/* 分页组件 */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between px-6 py-4 border-t border-white/10">
          <div className="text-sm text-gray-400">
            显示 {startIndex + 1}-{Math.min(endIndex, totalRobots)} 条，共 {totalRobots} 条
          </div>
          <Pagination className="justify-end">
            <PaginationContent className="gap-1">
              <PaginationItem>
                <PaginationPrevious 
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  className={cn(
                    "bg-transparent border-gray-600 text-gray-400 hover:bg-white/10 hover:text-white",
                    currentPage === 1 && "opacity-50 cursor-not-allowed"
                  )}
                />
              </PaginationItem>
              
              {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                <PaginationItem key={page}>
                  <PaginationLink
                    onClick={() => setCurrentPage(page)}
                    isActive={currentPage === page}
                    className={cn(
                      "bg-transparent border-gray-600 text-gray-400 hover:bg-white/10 hover:text-white",
                      currentPage === page && "bg-blue-600 text-white hover:bg-blue-500 border-blue-600"
                    )}
                  >
                    {page}
                  </PaginationLink>
                </PaginationItem>
              ))}
              
              <PaginationItem>
                <PaginationNext 
                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                  className={cn(
                    "bg-transparent border-gray-600 text-gray-400 hover:bg-white/10 hover:text-white",
                    currentPage === totalPages && "opacity-50 cursor-not-allowed"
                  )}
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      )}
    </div>
  );
}
