"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { ChevronDown, SlidersHorizontal, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { CustomInput } from "@/components/ui/custom-input";
import { AgentSelect } from "@/components/ui/agent-select";
import { agentService } from "@/services/agent";
import { AgentUsedName } from "@/types/api";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { regionService } from "@/services/region";
import { ApiClientError } from "@/services/api";

export function RegionSelector() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [searchQuery, setSearchQuery] = useState("");
  const [province, setProvince] = useState("");
  const [city, setCity] = useState("");
  const [county] = useState("全区域");

  // API data states
  const [provinces, setProvinces] = useState<string[]>([]);
  const [cities, setCities] = useState<string[]>([]);
  const [agents, setAgents] = useState<AgentUsedName[]>([]);

  // Loading states
  const [provincesLoading, setProvincesLoading] = useState(true);
  const [citiesLoading, setCitiesLoading] = useState(false);
  const [agentsLoading, setAgentsLoading] = useState(false);

  // Error states
  const [error, setError] = useState<string | null>(null);

  // Load provinces and agents on component mount
  useEffect(() => {
    const loadProvinces = async () => {
      try {
        setProvincesLoading(true);
        setError(null);
        const provinceList = await regionService.getProvinces();
        setProvinces(provinceList);

        // Set first province as default if available
        if (provinceList.length > 0 && !province) {
          setProvince(provinceList[0]);
        }
      } catch (err) {
        const errorMessage = err instanceof ApiClientError
          ? err.message
          : '加载省份列表失败';
        setError(errorMessage);
        console.error('Failed to load provinces:', err);
      } finally {
        setProvincesLoading(false);
      }
    };

    const loadAgents = async () => {
      try {
        setAgentsLoading(true);
        const stageType = searchParams.get('stageType') as 'UNKNOWN' | 'PRESCHOOL' | 'PRIMARY' || 'PRIMARY';
        const agentList = await agentService.getAgentList(stageType);
        setAgents(agentList);
      } catch (err) {
        console.error('Failed to load agents:', err);
      } finally {
        setAgentsLoading(false);
      }
    };

    loadProvinces();
    loadAgents();
  }, [searchParams]);

  // Load cities when province changes
  useEffect(() => {
    const loadCities = async () => {
      if (!province) {
        setCities([]);
        setCity("");
        return;
      }

      try {
        setCitiesLoading(true);
        setError(null);
        const cityList = await regionService.getCities({ province });
        setCities(cityList);

        // Set first city as default if available
        if (cityList.length > 0) {
          setCity(cityList[0]);
        } else {
          setCity("");
        }
      } catch (err) {
        const errorMessage = err instanceof ApiClientError
          ? err.message
          : '加载城市列表失败';
        setError(errorMessage);
        console.error('Failed to load cities:', err);
        setCities([]);
        setCity("");
      } finally {
        setCitiesLoading(false);
      }
    };

    loadCities();
  }, [province]);

  // Handle province selection
  const handleProvinceSelect = (selectedProvince: string) => {
    setProvince(selectedProvince);
    // City will be automatically loaded by the useEffect above
  };

  // Handle city selection
  const handleCitySelect = (selectedCity: string) => {
    setCity(selectedCity);
  };

  // Handle agent selection
  const handleAgentSelect = (agentId: number, agentName: string) => {
    setSearchQuery(agentName);
  };

  // 处理搜索提交
  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // 获取当前URL中的stageType参数
    const stageType = searchParams.get('stageType');
    
    // 构建基本查询参数
    let queryParams = `province=${encodeURIComponent(province)}&city=${encodeURIComponent(city)}&county=${encodeURIComponent(county)}&query=${encodeURIComponent(searchQuery)}`;
    
    // 如果存在stageType参数，则保留它
    if (stageType) {
      queryParams += `&stageType=${encodeURIComponent(stageType)}`;
    }
    
    // 跳转到代理商列表页面，并携带参数
    router.push(`/courseware/agent/list?${queryParams}`);
  };

  return (
    <div className="w-[99%]">
      {/* Error display */}
      {error && (
        <div className="mb-2 px-4 py-2 bg-red-500/20 border border-red-500/30 rounded-lg">
          <p className="text-red-300 text-sm">{error}</p>
        </div>
      )}

      <div
        className="px-4 py-3 bg-gradient-to-r from-gray-800 via-gray-700 to-gray-800 shadow-md rounded-2xl"
        style={{
          background: 'linear-gradient(90deg, rgba(29, 31, 31, 1) 0%, rgba(53, 56, 59, 1) 26.11%, rgba(29, 30, 31, 1) 100%)'
        }}
      >
      <div className="flex items-center justify-between gap-4">
        {/* 左侧：绿色圆形头像 + 提示文本 */}
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center text-white">
            <SlidersHorizontal className="h-5 w-5" />
          </div>
          <span className="text-white text-sm whitespace-nowrap">您要找的是......</span>
        </div>

        {/* 中间：三个区域选择 */}
        <div className="flex items-center gap-4">
          {/* 省级区域 */}
          <div className="relative flex items-center gap-2">
            <span 
              className="text-xs font-medium h-8 flex items-center px-3"
              style={{ color: 'rgba(110, 227, 86, 1)' }}
            >
              省级区域
            </span>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="bg-transparent border-0 text-white hover:bg-green-500/20 h-8 px-3 flex items-center gap-1 focus:outline-none focus:ring-0 focus:border-0 focus-visible:ring-0 focus-visible:ring-offset-0"
                  disabled={provincesLoading}
                >
                  {provincesLoading ? (
                    <>
                      <Loader2 className="h-3 w-3 animate-spin" />
                      加载中...
                    </>
                  ) : (
                    <>
                      {province || "请选择省份"}
                      <ChevronDown className="h-3 w-3" />
                    </>
                  )}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="start" className="w-32 max-h-60 overflow-y-auto">
                {provinces.map((prov) => (
                  <DropdownMenuItem
                    key={prov}
                    onClick={() => handleProvinceSelect(prov)}
                    className={province === prov ? "bg-green-500/20" : ""}
                  >
                    {prov}
                  </DropdownMenuItem>
                ))}
                {provinces.length === 0 && !provincesLoading && (
                  <DropdownMenuItem disabled>
                    暂无数据
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
            <div 
              className="absolute bottom-0 left-0 right-0 h-0.5"
              style={{ backgroundColor: 'rgba(110, 227, 86, 1)' }}
            />
          </div>

          {/* 市级区域 */}
          <div className="relative flex items-center gap-2">
            <span 
              className="text-xs font-medium h-8 flex items-center px-3"
              style={{ color: 'rgba(110, 227, 86, 1)' }}
            >
              市级区域
            </span>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="bg-transparent border-0 text-white hover:bg-green-500/20 h-8 px-3 flex items-center gap-1 font-bold focus:outline-none focus:ring-0 focus:border-0 focus-visible:ring-0 focus-visible:ring-offset-0"
                  disabled={citiesLoading || !province}
                >
                  {citiesLoading ? (
                    <>
                      <Loader2 className="h-3 w-3 animate-spin" />
                      加载中...
                    </>
                  ) : (
                    <>
                      {city || (!province ? "请先选择省份" : "请选择城市")}
                      <ChevronDown className="h-3 w-3" />
                    </>
                  )}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="start" className="w-32 max-h-60 overflow-y-auto">
                {cities.map((cityItem) => (
                  <DropdownMenuItem
                    key={cityItem}
                    onClick={() => handleCitySelect(cityItem)}
                    className={city === cityItem ? "bg-green-500/20" : ""}
                  >
                    {cityItem}
                  </DropdownMenuItem>
                ))}
                {cities.length === 0 && !citiesLoading && province && (
                  <DropdownMenuItem disabled>
                    暂无数据
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
            <div 
              className="absolute bottom-0 left-0 right-0 h-0.5"
              style={{ backgroundColor: 'rgba(110, 227, 86, 1)' }}
            />
          </div>

          {/* 县级区域 */}
          <div className="relative flex items-center gap-2">
            <span className="text-xs font-medium text-gray-300 h-8 flex items-center px-3">
              县级区域
            </span>
            <div className="h-8 px-3 flex items-center gap-1 text-gray-500 cursor-not-allowed">
              <span className="text-sm">{county}</span>
              <ChevronDown className="h-3 w-3 opacity-50" />
            </div>
            <div 
              className="absolute bottom-0 left-0 right-0 h-0.5 bg-gray-500"
            />
          </div>
        </div>

        {/* 右侧：搜索输入框和按钮 */}
        <div className="flex-shrink-0">
          <form onSubmit={handleSearchSubmit} className="flex items-center gap-2">
                <AgentSelect
                  value={searchQuery}
                  onChange={handleAgentSelect}
                  agents={agents}
                  placeholder="请输入代理商名称"
                  height="h-[37px]"
                />
            <button 
              type="submit"
              className="h-10 w-10 hover:bg-white/10 rounded-lg flex items-center justify-center transition-colors"
            >
              <img 
                src="/images/search.btn.svg" 
                alt="搜索" 
                className="h-6 w-6"
              />
              <span className="sr-only">搜索</span>
            </button>
          </form>
        </div>
      </div>
    </div>
    </div>
  );
}