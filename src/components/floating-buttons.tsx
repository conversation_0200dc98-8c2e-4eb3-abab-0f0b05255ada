"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { ChevronLeft, ArrowUpToLine } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

export function FloatingButtons() {
  const router = useRouter();
  const [showScrollTop, setShowScrollTop] = useState(false);

  // 监听主内容区域的滚动事件
  useEffect(() => {
    const handleScroll = () => {
      const scrollContainer = document.querySelector('.fixed.bottom-0.right-0.overflow-y-auto');
      if (scrollContainer) {
        const scrollTop = scrollContainer.scrollTop;
        setShowScrollTop(scrollTop > 200);
      }
    };

    // 查找滚动容器并添加监听器
    const scrollContainer = document.querySelector('.fixed.bottom-0.right-0.overflow-y-auto');
    if (scrollContainer) {
      scrollContainer.addEventListener('scroll', handleScroll);
      return () => scrollContainer.removeEventListener('scroll', handleScroll);
    }
  }, []);

  // 返回上一页
  const handleGoBack = () => {
    if (window.history.length > 1) {
      router.back();
    } else {
      router.push("/");
    }
  };

  // 回到顶部
  const handleScrollToTop = () => {
    // 精确定位AppLayout中的主内容滚动容器
    // 这个容器有 fixed、bottom-0、right-0、overflow-y-auto 等类
    const scrollContainer = document.querySelector('.fixed.bottom-0.right-0.overflow-y-auto');
    if (scrollContainer) {
      scrollContainer.scrollTo({
        top: 0,
        behavior: "smooth",
      });
    } else {
      // 备用方案：查找任何带有overflow-y-auto的元素
      const fallbackContainer = document.querySelector('[class*="overflow-y-auto"]');
      if (fallbackContainer) {
        fallbackContainer.scrollTo({
          top: 0,
          behavior: "smooth",
        });
      }
    }
  };

  return (
    <div className="fixed bottom-5 right-5 z-50 flex flex-col gap-3">
      {/* 返回按钮 */}
      <Button
        onClick={handleGoBack}
        size="icon"
        className={cn(
          "h-12 w-12 rounded-full shadow-lg transition-all duration-300",
          "bg-black text-green-500 hover:bg-black/90",
          "hover:scale-110 active:scale-95",
          "border border-gray-600",
          "[&_svg]:!size-8"
        )}
        aria-label="返回上一页"
      >
        <ChevronLeft className="size-8" />
      </Button>

      {/* 回到顶部按钮 - 根据滚动状态显示 */}
      <Button
        onClick={handleScrollToTop}
        size="icon"
        className={cn(
          "h-12 w-12 rounded-full shadow-lg transition-all duration-300",
          "bg-black text-green-500 hover:bg-black/90",
          "hover:scale-110 active:scale-95",
          "border border-gray-600",
          "[&_svg]:!size-6",
          // 根据滚动位置控制显示/隐藏
          showScrollTop
            ? "opacity-100 translate-y-0 pointer-events-auto"
            : "opacity-0 translate-y-2 pointer-events-none"
        )}
        aria-label="回到顶部"
      >
        <ArrowUpToLine className="size-6" />
      </Button>
    </div>
  );
}
