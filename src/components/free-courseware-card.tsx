"use client";

import { <PERSON><PERSON><PERSON>, Check } from "lucide-react";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

interface Course {
  id: string;
  name: string;
  authorized: boolean;
}

interface FreeCoursewareInfo {
  courses: Course[];
}

interface FreeCoursewareCardProps {
  coursewareInfo: FreeCoursewareInfo;
}

export function FreeCoursewareCard({ coursewareInfo }: FreeCoursewareCardProps) {
  return (
    <Card className="overflow-hidden border-muted bg-card text-card-foreground shadow-sm">
      <CardContent className="pt-6">
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
          {coursewareInfo.courses.map((course) => (
            <Card key={course.id} className={cn(
              "transition-colors border-none",
              course.authorized ? "bg-card/80" : "bg-card/50"
            )}>
              <div className="relative">
                <CardContent className="px-24 py-1 flex justify-between items-center gap-2">
                  <span className="font-medium">{course.name}</span>
                  <Badge 
                    variant={course.authorized ? "default" : "destructive"}
                    className={cn(
                      "transition-colors flex-shrink-0",
                      course.authorized 
                        ? "bg-green-600/90 hover:bg-green-600 text-white" 
                        : "bg-destructive/90 hover:bg-destructive text-destructive-foreground"
                    )}
                  >
                    {course.authorized ? "已开启" : "未开启"}
                  </Badge>
                </CardContent>
                <div className="absolute bottom-0 left-20 right-20 h-px bg-green-500" />
              </div>
            </Card>
          ))}
        </div>
      </CardContent>
      
      <CardFooter className="flex justify-end pt-2 pb-4">
        <div className="text-xs text-muted-foreground flex items-center gap-1">
          <BookOpen className="size-3" />
          <span>共 {coursewareInfo.courses.length} 个课程</span>
        </div>
      </CardFooter>
    </Card>
  );
}