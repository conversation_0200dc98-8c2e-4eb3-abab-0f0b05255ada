"use client";

import { Menu } from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import { SnCodeManager } from "./sn-code-manager";

interface HeaderProps {
  onMenuToggle?: () => void;
}

export function Header({ onMenuToggle }: HeaderProps) {

  return (
    <header className="fixed top-0 left-0 right-0 z-30 h-20  shadow-md bg-black ">
      {/* 背景图片层 - 使用clip-path排除Logo区域 */}
      <div
        className="absolute top-0 left-0 w-full h-full bg-cover"
        style={{
          backgroundImage: 'linear-gradient(to right, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0)), url(/images/banner.png)',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          clipPath: 'polygon(0px 0%, 100% 0%, 100% 100%, 300px 100%)'
        }}
      />

      <div className="flex items-center relative z-10 h-full ">
        <div className="flex items-center space-x-1 bg-black px-1 rounded">
          {onMenuToggle && (
            <Button
              variant="ghost"
              size="icon"
              className="lg:hidden text-white hover:bg-teal-700 h-6 w-6"
              onClick={onMenuToggle}
              aria-label="切换菜单"
            >
              <Menu className="h-4 w-4" />
              <span className="sr-only">切换菜单</span>
            </Button>
          )}
          <Link href="/" className="flex items-center space-x-1 hover:opacity-80 transition-opacity">
            <Image
              src="/images/logo_icon_only.svg"
              alt="EV∅LVER Logo"
              width={120}
              height={18}
              className="object-contain"
              
            />
            <span
              className="inline-block w-[3px] h-[30px] bg-white  align-middle"
              aria-hidden="true">
              
             </span>
            <span className="text-base font-bold text-white truncate text-[20px]">
              进化者
            </span>
          </Link>
        </div>

        <div className="flex-1 flex justify-center">
          <SnCodeManager />
        </div>
      </div>

    </header>
  );
}
