"use client";

import { useState, useEffect } from "react";
import { Toaster } from "@/components/ui/sonner";
import { usePathname } from "next/navigation";
import { Header } from "./header";
import { Sidebar } from "./sidebar";
import { RegionSelector } from "./region-selector";
import { FloatingButtons } from "./floating-buttons";
import { cn } from "@/lib/utils";

interface AppLayoutProps {
  children: React.ReactNode;
}

export function AppLayout({ children }: AppLayoutProps) {
  // 获取当前路径
  const pathname = usePathname();
  
  // 定义不显示RegionSelector的页面
  const hideRegionSelectorPages = ['/', '/courseware/upload', '/courseware/free', '/courseware/robot/detail'];
  const showRegionSelector = !hideRegionSelectorPages.includes(pathname);
  
  // 侧边栏状态
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  // 屏幕尺寸状态
  const [isMobile, setIsMobile] = useState(false);

  // 监听窗口大小变化
  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 1024);
      // 在移动设备上默认关闭侧边栏
      if (window.innerWidth < 1024) {
        setIsSidebarOpen(false);
      } else {
        setIsSidebarOpen(true);
      }
    };

    // 初始检查
    checkScreenSize();

    // 添加窗口大小变化监听器
    window.addEventListener('resize', checkScreenSize);

    // 清理监听器
    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  // 处理侧边栏切换
  const handleSidebarToggle = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  // 处理点击主内容区域关闭侧边栏（仅在移动设备上）
  const handleMainContentClick = () => {
    if (isMobile && isSidebarOpen) {
      setIsSidebarOpen(false);
    }
  };

  return (
    <div className="relative min-h-screen bg-background text-foreground antialiased">
      {/* Toaster 组件 */}
      <Toaster position="top-right" closeButton richColors />
      {/* 固定Header */}
      <Header onMenuToggle={handleSidebarToggle} />

      {/* 固定Sidebar */}
      <Sidebar isOpen={isSidebarOpen} onClose={() => setIsSidebarOpen(false)} />
  
      {/* 侧边栏遮罩层（仅在移动设备上显示） */}
      {isMobile && isSidebarOpen && (
        <div 
          className="fixed inset-0 z-40 bg-black/50 transition-opacity duration-300 lg:hidden"
          onClick={handleSidebarToggle}
          aria-hidden="true"
        />
      )}
      
      {/* 条件渲染RegionSelector */}
      {showRegionSelector && (
        <div 
          className={cn(
            "fixed top-24 right-0 z-10 transition-all duration-300",
            // 桌面端：始终为侧边栏留出空间
            // 移动端：根据侧边栏状态调整
            isMobile 
              ? (isSidebarOpen ? "left-0" : "left-0")
              : "left-64"
          )}
        >
          <RegionSelector />
        </div>
      )}
      
      {/* 主内容区域 - 可滚动 */}
      <div 
        className={cn(
          "fixed bottom-0 right-0 overflow-y-auto transition-all duration-300",
          // 隐藏滚动条但保持滚动功能
          "scrollbar-hide",
          // 根据是否显示RegionSelector动态调整top值
          showRegionSelector ? "top-36" : "top-16", // 有RegionSelector时top-40，否则top-24
          // 桌面端：始终为侧边栏留出空间
          // 移动端：根据侧边栏状态调整
          isMobile 
            ? (isSidebarOpen ? "left-0" : "left-0")
            : "left-64"
        )}
        onClick={handleMainContentClick}
        style={{
          /* 隐藏滚动条的CSS */
          scrollbarWidth: 'none', /* Firefox */
          msOverflowStyle: 'none', /* IE and Edge */
        }}
      >
        <main className="container mx-auto p-4 md:p-6 lg:p-8 min-h-full">
          {children}
        </main>
      </div>
      
      {/* 浮动按钮 */}
      <FloatingButtons />
    </div>
  );
}