"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { CustomInput } from "@/components/ui/custom-input";
import { CustomButton } from "@/components/ui/custom-button";

export function SnCodeManager() {
  const [snCode, setSnCode] = useState("");
  const router = useRouter();

  // 处理SN码提交
  const handleSnSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // 验证SN码不为空
    if (!snCode.trim()) {
      console.log("请输入SN码");
      return;
    }
    
    // 跳转到机器人管理页面，传递SN码参数
    router.push(`/courseware/robot/detail?sn=${encodeURIComponent(snCode.trim())}`);
  };

  return (
    <div className="flex flex-col items-center">
      {/* 第一行：标题 */}
      <div className="text-center">
        <span className="text-base font-medium text-white whitespace-nowrap">
          使用SN码或ID单独管理机器人
        </span>
      </div>
      
      {/* 第二行：输入框和按钮重叠 */}
      <form onSubmit={handleSnSubmit} className="relative">
        <CustomInput 
          type="text" 
          placeholder="输入SN码或机器人ID"
          value={snCode}
          onChange={(e) => setSnCode(e.target.value)}
          className="w-[270px] pr-20"
        />
        <CustomButton 
          type="submit"
          className="absolute right-1 top-1/2 transform -translate-y-1/2"
        >
          去管理
        </CustomButton>
      </form>
    </div>
  );
}
