"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { CustomInput } from "@/components/ui/custom-input";
import { TablePagination } from "@/components/ui/table-pagination";
import { cn } from "@/lib/utils";
import { snService } from "@/services/sn";
import { ApiClientError } from "@/services/api";
import { toastService } from "@/services/toast";
import { CheckCheck } from "lucide-react";

interface Robot {
  id: string;
  sn: string;
  location: string;
  lastContact: string;
}

interface NoBelongRobotsListProps {
  robots: Robot[];
  totalRobots: number;
  onConfirm?: (robotId: string, sn: string) => void;
  onCancel?: (robotId: string) => void;
  pageSize?: number;
}

export function NoBelongRobotsList({ 
  robots,
  totalRobots,
  onConfirm,
  onCancel,
  pageSize = 10 // 默认每页显示10条数据
}: NoBelongRobotsListProps) {
  // 分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const totalPages = Math.ceil(totalRobots / pageSize);
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const currentRobots = robots.slice(startIndex, endIndex);
  
  // 用于跟踪每行的SN输入值
  const [snInputs, setSnInputs] = useState<Record<string, string>>(
    robots.reduce((acc, robot) => ({ ...acc, [robot.id]: robot.sn || '' }), {})
  );
  
  // 用于跟踪哪一行正在进行二次确认
  const [confirmingRows, setConfirmingRows] = useState<Record<string, boolean>>(
    robots.reduce((acc, robot) => ({ ...acc, [robot.id]: false }), {})
  );

  // 用于跟踪API调用状态
  const [submittingRows, setSubmittingRows] = useState<Record<string, boolean>>({});
  const [successRows, setSuccessRows] = useState<Record<string, boolean>>({});
  
  // 处理SN输入变化
  const handleSnChange = (robotId: string, value: string) => {
    setSnInputs(prev => ({ ...prev, [robotId]: value }));
    // 如果正在二次确认状态，输入变化时取消二次确认
    if (confirmingRows[robotId]) {
      setConfirmingRows(prev => ({ ...prev, [robotId]: false }));
    }
    // 输入变化时清除成功状态
    setSuccessRows(prev => ({ ...prev, [robotId]: false }));
  };
  
  // 处理首次确认操作
  const handleFirstConfirm = (robotId: string) => {
    const sn = snInputs[robotId];
    if (sn && sn.trim()) {
      // 如果SN已填写，启用二次确认
      setConfirmingRows(prev => ({ ...prev, [robotId]: true }));
    }
  };
  
  // 处理最终确认操作
  const handleFinalConfirm = async (robotId: string) => {
    const sn = snInputs[robotId];
    if (!sn || !sn.trim()) {
      return;
    }

    try {
      // 设置提交状态
      setSubmittingRows(prev => ({ ...prev, [robotId]: true }));

      // 调用 API 补充 SN
      await snService.supplementSn({
        robotId: robotId,
        sn: sn.trim()
      });

      // API 调用成功
      console.log(`Successfully supplemented SN for robot ${robotId}: ${sn}`);

      // 设置成功状态
      setSuccessRows(prev => ({ ...prev, [robotId]: true }));

      // 调用原有的确认回调
      if (onConfirm) {
        onConfirm(robotId, sn);
      }

      // 确认后取消二次确认状态
      setConfirmingRows(prev => ({ ...prev, [robotId]: false }));

    } catch (error) {
      // 处理错误 - 不需要在这里显示toast，因为api.ts中已经处理了错误提示
      console.error(`Failed to supplement SN for robot ${robotId}:`, error);

    } finally {
      // 清除提交状态
      setSubmittingRows(prev => ({ ...prev, [robotId]: false }));
    }
  };
  
  // 处理取消操作
  const handleCancelConfirm = (robotId: string) => {
    // 取消二次确认状态
    setConfirmingRows(prev => ({ ...prev, [robotId]: false }));
    // 清除成功状态
    setSuccessRows(prev => ({ ...prev, [robotId]: false }));
    if (onCancel) {
      onCancel(robotId);
    }
  };
  
  return (
    <div className="w-full space-y-2">
      <div className="rounded-lg overflow-hidden" style={{ background: 'rgba(16, 15, 15, 0.98)', backdropFilter: 'blur(50px)' }}>
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="border-b border-white/20 hover:bg-white/5">
                <TableHead className="font-medium text-gray-400 px-6 py-2">机器人ID</TableHead>
                <TableHead className="font-medium text-gray-400 px-6 py-2">机器人最后联网时间</TableHead>
                <TableHead className="font-medium text-gray-400 px-6 py-2">机器人最后获取位置</TableHead>
                <TableHead className="font-medium text-gray-400 px-6 py-2 min-w-[250px]">填写机器人SN</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {currentRobots.map((robot: Robot) => (
                <TableRow 
                  key={robot.id} 
                  className="border-b border-white/10 hover:bg-white/5"
                >
                  <TableCell className="px-6 py-2 font-mono text-sm text-white">
                    {robot.id}
                  </TableCell>
                  <TableCell className="px-6 py-2 text-sm text-white/80">
                    {robot.lastContact}
                  </TableCell>
                  <TableCell className="px-6 py-2 text-sm text-white/80">
                    {robot.location}
                  </TableCell>
                  <TableCell className="px-6 py-2">
                    <div className="relative">
                      <CustomInput
                        value={snInputs[robot.id]}
                        onChange={(e) => handleSnChange(robot.id, e.target.value)}
                        placeholder="请输入SN码"
                        className="h-8 pr-16"
                        disabled={submittingRows[robot.id]}
                      />
                      {!confirmingRows[robot.id] ? (
                        <Button
                          size="sm"
                          onClick={() => handleFirstConfirm(robot.id)}
                          disabled={submittingRows[robot.id]}
                          className={cn(
                            "absolute right-1 top-1/2 -translate-y-1/2 border-0 px-3 h-6 text-xs disabled:opacity-50",
                            successRows[robot.id]
                              ? "bg-green-700 hover:bg-green-600 text-green-200"
                              : "bg-green-600 hover:bg-green-500 text-white"
                          )}
                        >
                          {successRows[robot.id] ? (
                            <CheckCheck className="h-3 w-3" />
                          ) : (
                            "确认"
                          )}
                        </Button>
                      ) : (
                        <div className="absolute right-1 top-1/2 -translate-y-1/2 flex gap-1">
                          <Button
                            size="sm"
                            onClick={() => handleFinalConfirm(robot.id)}
                            disabled={submittingRows[robot.id]}
                            className="bg-green-600 hover:bg-green-500 text-white border-0 px-1.5 h-6 text-xs disabled:opacity-50"
                          >
                            {submittingRows[robot.id] ? '提交中...' : '确认'}
                          </Button>
                          <Button
                            size="sm"
                            onClick={() => handleCancelConfirm(robot.id)}
                            disabled={submittingRows[robot.id]}
                            className="bg-red-600 hover:bg-red-500 text-white border-0 px-1.5 h-6 text-xs disabled:opacity-50"
                          >
                            取消
                          </Button>
                        </div>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
      
      {/* 使用公共分页组件 */}
      <TablePagination
        currentPage={currentPage}
        totalPages={totalPages}
        totalItems={totalRobots}
        pageSize={pageSize}
        onPageChange={setCurrentPage}
      />
    </div>
  );
}