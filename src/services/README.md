# API Services

This directory contains API service modules for the EvoConsole Hub application.

## Structure

- `api.ts` - Base API client with common HTTP functionality
- `region.ts` - Region-specific API calls (provinces, cities)
- `agent.ts` - Agent-specific API calls (find agents, agent details)
- `sn.ts` - SN management API calls (supplement SN, lock robots)

## Base API Client (`api.ts`)

The `ApiClient` class provides a base HTTP client with:
- GET and POST request methods
- Automatic error handling
- Response validation
- TypeScript support

### Usage

```typescript
import { apiClient } from '@/services/api';

// GET request
const data = await apiClient.get<string[]>('/region/province');

// GET request with parameters
const cities = await apiClient.get<string[]>('/region/city', { province: '四川省' });
```

## Region Service (`region.ts`)

The `RegionService` class provides methods for:
- `getProvinces()` - Fetch all provinces
- `getCities(params)` - Fetch cities for a specific province

### Usage

```typescript
import { regionService } from '@/services/region';

// Get provinces
const provinces = await regionService.getProvinces();

// Get cities for a province
const cities = await regionService.getCities({ province: '四川省' });
```

## Agent Service (`agent.ts`)

The `AgentService` class provides methods for:
- `findAgents(params)` - Find agents based on search criteria
- `getAgentList(stageType)` - Get agent list by stage type
- `getAgentDetail(params)` - Get agent details

### Usage

```typescript
import { agentService } from '@/services/agent';

// Find agents with search criteria
const agents = await agentService.findAgents({
  province: '四川省',
  city: '成都市',
  agentName: '科技',
  stageType: 'PRIMARY'
});

// Get agent list by stage type
const agentList = await agentService.getAgentList('PRESCHOOL');
```

## SN Service (`sn.ts`)

The `SnService` class provides methods for:
- `supplementSn(params)` - Supplement SN for a robot
- `lockRobot(sn)` - Lock a robot by SN

### Usage

```typescript
import { snService } from '@/services/sn';

// Supplement SN for a robot
await snService.supplementSn({
  robotId: '10000007893',
  sn: 'D7DM8365DL'
});

// Lock a robot by SN
await snService.lockRobot('D7DM8365DL');
```

## Error Handling

All API calls can throw `ApiClientError` instances with:
- `message` - Error description
- `code` - API error code
- `timestamp` - Error timestamp

## API Base URL

The API base URL is configured as: `https://m1.apifoxmock.com/m1/6852950-6567297-default`

## Response Format

All API responses follow the standard format:
```typescript
{
  code: number;
  message: string;
  data: T; // Actual response data
  timestamp: number;
  success: boolean;
}
```
