/**
 * Agent API Service
 * Handles agent-related API calls
 */

import { apiClient } from './api';
import { CourseAgentSearchReq, AgentVO, AgentDetail, CourseGrade, AgentUsedName } from '@/types/api';

/**
 * Agent service class
 */
export class AgentService {
  /**
   * Find agents based on search criteria
   * GET /agent/find (Note: API doc shows this as GET but with requestBody, treating as POST)
   */
  async findAgents(params: CourseAgentSearchReq): Promise<AgentVO[]> {
    // Based on the OpenAPI spec, this should be a POST request with body
    // even though the path suggests GET. Following the spec structure.
    return apiClient.post<AgentVO[]>('/agent/find', params);
  }

  /**
   * Get agent list by stage type
   * GET /agent/list?stageType={stageType}
   */
  async getAgentList(stageType: 'UNKNOWN' | 'PRESCHOOL' | 'PRIMARY'): Promise<AgentUsedName[]> {
    return apiClient.get<AgentUsedName[]>('/agent/list', { stageType });
  }

  /**
   * Get agent details
   * GET /agent/detail (with requestBody)
   */
  async getAgentDetail(params: CourseAgentSearchReq): Promise<AgentDetail> {
    return apiClient.post<AgentDetail>('/agent/detail', params);
  }

  /**
   * Get agent courses
   * POST /agent/course
   */
  async getAgentCourses(params: CourseAgentSearchReq): Promise<CourseGrade[]> {
    return apiClient.post<CourseGrade[]>('/agent/course', params);
  }
}

// Export a default instance
export const agentService = new AgentService();
