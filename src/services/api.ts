/**
 * Base API Client
 * Handles HTTP requests to the 进化者课件授权平台 API
 */

import { ApiResponse } from '@/types/api';
import { toastService } from './toast';

// API base URL from the provided mock server
const API_BASE_URL = 'https://m1.apifoxmock.com/m1/6852950-6567297-default';

/**
 * Custom error class for API errors
 */
export class ApiClientError extends Error {
  constructor(
    message: string,
    public code: number,
    public timestamp: number
  ) {
    super(message);
    this.name = 'ApiClientError';
  }
}

/**
 * Base API client with common functionality
 */
export class ApiClient {
  private baseUrl: string;

  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl;
  }

  /**
   * Make a GET request
   */
  async get<T>(endpoint: string, params?: Record<string, string>): Promise<T> {
    const url = new URL(`${this.baseUrl}${endpoint}`);
    
    // Add query parameters if provided
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        url.searchParams.append(key, value);
      });
    }

    try {
      const response = await fetch(url.toString(), {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: ApiResponse<T> = await response.json();

      // Check if the API response indicates success
      if (!data.success) {
        throw new ApiClientError(
          data.message || 'API request failed',
          data.code,
          data.timestamp
        );
      }

      return data.data;
    } catch (error) {
      if (error instanceof ApiClientError) {
        // 使用toast显示API错误
        toastService.error(error.message);
        throw error;
      }
      
      // Handle network errors or other fetch errors
      const apiError = new ApiClientError(
        error instanceof Error ? error.message : '未知错误',
        -1,
        Date.now()
      );
      
      // 使用toast显示网络错误
      toastService.error(apiError.message);
      throw apiError;
    }
  }

  /**
   * Make a POST request
   */
  async post<T>(endpoint: string, body?: any, params?: Record<string, string>): Promise<T> {
    const url = new URL(`${this.baseUrl}${endpoint}`);
    
    // Add query parameters if provided
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        url.searchParams.append(key, value);
      });
    }

    try {
      const response = await fetch(url.toString(), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: body ? JSON.stringify(body) : undefined,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: ApiResponse<T> = await response.json();

      if (!data.success) {
        throw new ApiClientError(
          data.message || 'API request failed',
          data.code,
          data.timestamp
        );
      }

      return data.data;
    } catch (error) {
      if (error instanceof ApiClientError) {
        // 使用toast显示API错误
        toastService.error(error.message);
        throw error;
      }
      
      // Handle network errors or other fetch errors
      const apiError = new ApiClientError(
        error instanceof Error ? error.message : '未知错误',
        -1,
        Date.now()
      );
      
      // 使用toast显示网络错误
      toastService.error(apiError.message);
      throw apiError;
    }
  }
}

// Export a default instance
export const apiClient = new ApiClient();
