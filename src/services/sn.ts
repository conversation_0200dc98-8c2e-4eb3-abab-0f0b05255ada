/**
 * SN (Serial Number) API Service
 * 处理序列号相关的API调用
 */

import { apiClient } from './api';
import { CourseGrade } from '@/types/api';

/**
 * 查询条件接口
 */
export interface CourseAgentSearchReq {
  province?: string;
  city?: string;
  area?: string;
  agenId?: number;
  sn?: string;
  stageType?: 'UNKNOWN' | 'PRESCHOOL' | 'PRIMARY';
  courseId?: number;
  robotIds?: string[];
}

/**
 * SN服务类
 */
export class SnService {
  /**
   * 获取SN课程授权状态
   * @param searchReq 查询条件
   * @returns 课程列表
   */
  async getSnCourse(searchReq: CourseAgentSearchReq): Promise<CourseGrade[]> {
    return await apiClient.post<CourseGrade[]>('/sn/course', searchReq);
  }

  /**
   * 锁机
   * @param sn SN码
   */
  async lockSn(sn: string): Promise<void> {
    return await apiClient.post<void>('/sn/lock', undefined, { sn });
  }

  /**
   * 解锁
   * @param sn SN码
   */
  async unlockSn(sn: string): Promise<void> {
    return await apiClient.post<void>('/sn/unlock', undefined, { sn });
  }

  /**
   * 补充序列号
   * @param robotId 机器人ID
   * @param sn 序列号
   */
  async supplementSn(robotId: string, sn: string): Promise<void> {
    return await apiClient.post<void>('/sn/supplement', {
      robotId,
      sn
    });
  }
}

// 导出默认实例
export const snService = new SnService();

// 导出便捷函数
export const getSnCourse = (searchReq: CourseAgentSearchReq) => 
  snService.getSnCourse(searchReq);

export const lockSn = (sn: string) => 
  snService.lockSn(sn);

export const unlockSn = (sn: string) => 
  snService.unlockSn(sn);

export const supplementSn = (robotId: string, sn: string) => 
  snService.supplementSn(robotId, sn);