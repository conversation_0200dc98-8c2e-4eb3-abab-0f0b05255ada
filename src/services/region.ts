/**
 * Region API Service
 * Handles region-related API calls (provinces, cities)
 */

import { apiClient } from './api';
import { GetCityListParams } from '@/types/api';

/**
 * Region service class
 */
export class RegionService {
  /**
   * Get list of all provinces
   * GET /region/province
   */
  async getProvinces(): Promise<string[]> {
    return apiClient.get<string[]>('/region/province');
  }

  /**
   * Get list of cities for a specific province
   * GET /region/city?province={provinceName}
   */
  async getCities(params: GetCityListParams): Promise<string[]> {
    return apiClient.get<string[]>('/region/city', {
      province: params.province
    });
  }
}

// Export a default instance
export const regionService = new RegionService();
