/**
 * Toast 通知服务
 * 提供统一的消息提示功能，替代原有的红色区域错误提示
 */

import { toast } from "sonner";

interface ToastOptions {
  duration?: number;
  id?: string;
  action?: {
    label: string;
    onClick: () => void;
  };
}

/**
 * Toast 服务类
 * 提供不同类型的消息提示方法
 */
class ToastService {
  /**
   * 显示成功消息
   */
  success(message: string, options?: ToastOptions) {
    return toast.success(message, options);
  }

  /**
   * 显示错误消息
   */
  error(message: string, options?: ToastOptions) {
    return toast.error(message, options);
  }

  /**
   * 显示警告消息
   */
  warning(message: string, options?: ToastOptions) {
    return toast.warning(message, options);
  }

  /**
   * 显示普通信息消息
   */
  info(message: string, options?: ToastOptions) {
    return toast.info(message, options);
  }

  /**
   * 显示加载中消息
   * @returns 返回一个函数，调用该函数可以更新或关闭该消息
   */
  loading(message: string, options?: ToastOptions) {
    return toast.loading(message, options);
  }

  /**
   * 显示自定义消息
   */
  custom(message: string, options?: ToastOptions) {
    return toast(message, options);
  }

  /**
   * 显示可撤销操作的消息
   */
  promise<T>(
    promise: Promise<T>,
    messages: {
      loading: string;
      success: string | ((data: T) => string);
      error: string | ((error: unknown) => string);
    },
    options?: ToastOptions
  ) {
    return toast.promise(promise, messages, options);
  }

  /**
   * 关闭指定ID的消息
   */
  dismiss(id?: string) {
    toast.dismiss(id);
  }
}

// 导出单例实例
export const toastService = new ToastService();