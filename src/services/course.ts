/**
 * Course Authorization API Service
 * Handles course authorization-related API calls
 */

import { apiClient } from './api';
import { CourseAuthorizationReq } from '@/types/api';

/**
 * Course service class
 */
export class CourseService {
  /**
   * Enable course for agent
   * POST /course-grade/agent/enabled
   */
  async enableAgentCourse(params: CourseAuthorizationReq): Promise<void> {
    return apiClient.post<void>('/course-grade/agent/enabled', params);
  }

  /**
   * Disable course for agent
   * POST /course-grade/agent/disabled
   */
  async disableAgentCourse(params: CourseAuthorizationReq): Promise<void> {
    return apiClient.post<void>('/course-grade/agent/disabled', params);
  }
}

// Export a default instance
export const courseService = new CourseService();
