import { AppLayout } from "@/components/app-layout";
import { FreeCoursewareCard } from "@/components/free-courseware-card";

// 模拟免费课件数据
const coursewareInfo = {
  courses: [
    {
      id: "1",
      name: "爱国主义教育",
      authorized: true,
    },
    {
      id: "2",
      name: "象棋课程",
      authorized: true,
    },
    {
      id: "3",
      name: "围棋课程",
      authorized: true,
    },
    {
      id: "4",
      name: "机器人的奥秘第一阶段",
      authorized: false,
    },
    {
      id: "5",
      name: "机器人的奥秘第一阶段",
      authorized: false,
    },
    {
      id: "6",
      name: "机器人的奥秘第一阶段",
      authorized: false,
    },
    {
      id: "7",
      name: "机器人的奥秘第一阶段",
      authorized: false,
    },
    {
      id: "8",
      name: "机器人的奥秘第一阶段",
      authorized: false,
    },
    {
      id: "9",
      name: "机器人的奥秘第一阶段",
      authorized: false,
    },
    {
      id: "10",
      name: "机器人的奥秘第一阶段",
      authorized: false,
    },
    {
      id: "11",
      name: "机器人的奥秘第一阶段",
      authorized: false,
    },
    {
      id: "12",
      name: "机器人的奥秘第一阶段",
      authorized: false,
    },
    {
      id: "13",
      name: "机器人的奥秘第一阶段",
      authorized: false,
    },
    {
      id: "14",
      name: "机器人的奥秘第一阶段",
      authorized: false,
    },
    {
      id: "15",
      name: "机器人的奥秘第一阶段",
      authorized: false,
    },
    {
      id: "16",
      name: "机器人的奥秘第一阶段",
      authorized: false,
    },
    {
      id: "17",
      name: "机器人的奥秘第一阶段",
      authorized: false,
    },
    {
      id: "18",
      name: "机器人的奥秘第一阶段",
      authorized: false,
    },
    {
      id: "19",
      name: "机器人的奥秘第一阶段",
      authorized: false,
    },
    {
      id: "20",
      name: "机器人的奥秘第一阶段",
      authorized: false,
    },
    {
      id: "21",
      name: "机器人的奥秘第一阶段",
      authorized: false,
    },
  ],
};

export default function FreeCoursewarePage() {
  return (
    <AppLayout>
      <div className="space-y-6">
        <FreeCoursewareCard coursewareInfo={coursewareInfo} />
      </div>
    </AppLayout>
  );
}
