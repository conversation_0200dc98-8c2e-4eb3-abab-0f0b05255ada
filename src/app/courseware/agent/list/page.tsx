"use client";

import { useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { AppLayout } from "@/components/app-layout";
import { AgentManagement, AgentData } from "@/components/agent-management";
import { agentService } from "@/services/agent";
import { ApiClientError } from "@/services/api";
import { AgentVO } from "@/types/api";

// 将AgentVO转换为AgentData的辅助函数
const convertAgentVOToAgentData = (agentVO: AgentVO): AgentData => {
  return {
    id: `${agentVO.province}-${agentVO.city}-${agentVO.agentName}`,
    companyName: agentVO.agentName === 'missingSn' ? '' :
                 agentVO.agentName === 'unassigned' ? '' : agentVO.agentName,
    location: `${agentVO.province} ${agentVO.city}`,
    robotCount: `${agentVO.bots}台`,
    snLost: agentVO.agentName === 'missingSn',
    // 保存代理商的具体信息，用于传递到详情页面
    agentInfo: {
      agentId: agentVO.agentId,
      agentName: agentVO.agentName,
      province: agentVO.province,
      city: agentVO.city,
      area: agentVO.area,
      bots: agentVO.bots
    }
  };
};

export default function Home() {
  const searchParams = useSearchParams();
  const [locationType, setLocationType] = useState(0);
  const [agentData, setAgentData] = useState<AgentData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 从URL获取搜索参数并调用API
  useEffect(() => {
    const loadAgents = async () => {
      try {
        setLoading(true);
        setError(null);

        // 从URL获取参数
        const province = searchParams.get('province') || '';
        const city = searchParams.get('city') || '';
        const county = searchParams.get('county') || '';
        const query = searchParams.get('query') || '';
        const stageType = searchParams.get('stageType') as 'UNKNOWN' | 'PRESCHOOL' | 'PRIMARY' || 'UNKNOWN';

        // 设置locationType
        if (query) {
          setLocationType(1);
        }

        // 构建API请求参数
        const searchReq = {
          province: province || undefined,
          city: city || undefined,
          area: county || undefined,
          agentName: query || undefined,
          stageType: stageType
        };

        // 调用API
        const agents = await agentService.findAgents(searchReq);

        // 转换数据格式
        const convertedData = agents.map(convertAgentVOToAgentData);
        setAgentData(convertedData);

      } catch (err) {
        const errorMessage = err instanceof ApiClientError
          ? err.message
          : '加载代理商列表失败';
        setError(errorMessage);
        console.error('Failed to load agents:', err);
      } finally {
        setLoading(false);
      }
    };

    loadAgents();
  }, [searchParams]);

  if (loading) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-white">加载中...</div>
        </div>
      </AppLayout>
    );
  }

  if (error) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-red-400">错误: {error}</div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        <AgentManagement agentData={agentData} locationType={locationType} />
      </div>
    </AppLayout>
  );
}
