"use client";

import { useState, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { AppLayout } from "@/components/app-layout";
import { RobotsTable } from "@/components/robots-table";
import { AgentIcon } from "@/components/ui/agent-icon";
import { MapPin } from "lucide-react";
import { RobotIcon } from "@/components/ui/robot-icon";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { cn } from "@/lib/utils";
import { CompanyEditDialog } from "@/components/company-edit-dialog";

// 模拟数据
const robotsData = {
  company: "dsdas",
  location: "四川省成都市",
  totalRobots: 15,
  robots: [
    { id: "10000007893", sn: "D7DM8365DL", location: "四川省成都市", lastContact: "2025年7月14日", selected: false },
    { id: "10000007894", sn: "D7DM8365DM", location: "四川省成都市", lastContact: "2025年7月14日", selected: true },
    { id: "10000007895", sn: "D7DM8365DN", location: "四川省成都市", lastContact: "2025年7月14日", selected: true },
    { id: "10000007896", sn: "D7DM8365DO", location: "四川省成都市", lastContact: "2025年7月14日", selected: true },
    { id: "10000007897", sn: "D7DM8365DP", location: "四川省成都市", lastContact: "2025年7月14日", selected: false },
    { id: "10000007898", sn: "D7DM8365DQ", location: "四川省成都市", lastContact: "2025年7月14日", selected: false },
    { id: "10000007899", sn: "D7DM8365DR", location: "四川省成都市", lastContact: "2025年7月14日", selected: false },
    { id: "10000007892", sn: "D7DM8365DS", location: "四川省成都市", lastContact: "2025年7月14日", selected: false },
    { id: "10000007891", sn: "D7DM8365DT", location: "四川省成都市", lastContact: "2025年7月14日", selected: false },
    { id: "10000007890", sn: "D7DM8365DU", location: "四川省成都市", lastContact: "2025年7月14日", selected: false },
    { id: "10000007889", sn: "D7DM8365DV", location: "四川省成都市", lastContact: "2025年7月14日", selected: false },
  ],
};

export default function RobotsPage() {
  const searchParams = useSearchParams();
  const [isLocked, setIsLocked] = useState(false);
  const [selectedRobots, setSelectedRobots] = useState<Record<string, boolean>>({});
  const [isCompanyDialogOpen, setIsCompanyDialogOpen] = useState(false);
  const [companyName, setCompanyName] = useState(robotsData.company);
  const [locationType, setLocationType] = useState(0);
  
  // 检查URL查询参数
  useEffect(() => {
    // 只检查query参数是否存在
    const query = searchParams.get('query');
    if (query) {
      setLocationType(1);
    }
  }, [searchParams]);

  // 处理单个选择变化
  const handleSelectChange = (robotId: string, selected: boolean) => {
    setSelectedRobots(prev => ({ ...prev, [robotId]: selected }));
  };

  // 处理全选变化
  const handleSelectAll = (selected: boolean) => {
    const newSelectedRobots = { ...selectedRobots };
    robotsData.robots.forEach(robot => {
      newSelectedRobots[robot.id] = selected;
    });
    setSelectedRobots(newSelectedRobots);
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* 顶部信息区域 */}
        <div className="w-full rounded-lg overflow-hidden bg-black/98 backdrop-blur-lg p-4">
          {/* 所有信息和操作按钮放在同一行 */}
          <div className="flex flex-wrap items-center gap-4">
            {/* 左侧信息区域 */}
            <div className="flex flex-wrap items-center gap-8 flex-1">
              {/* 公司信息 */}
              <div className="relative flex-shrink-0">
                <div 
                  className={cn(
                    "flex items-center gap-2 h-8 px-2",
                    !companyName && "cursor-pointer hover:bg-white/10 rounded transition-colors"
                  )}
                  onClick={() => !companyName && setIsCompanyDialogOpen(true)}
                >
                  <AgentIcon 
                    color={companyName ? "white" : "#ef4444"}
                    className="size-6"
                  />
                  <span className="text-sm text-white">
                    {companyName || "<暂无归属代理商>"}
                  </span>
                </div>
                <div className="absolute bottom-0 left-2 right-2 h-px bg-green-500" />
              </div>
              
              {/* 位置信息 */}
              <div className="relative flex-shrink-0">
                <div className="flex items-center gap-2 h-8 px-2">
                  <MapPin className="size-6 text-white" />
                  <span className="text-sm text-white">{robotsData.location}</span>
                </div>
                <div className="absolute bottom-0 left-2 right-2 h-px bg-green-500" />
              </div>
              
              {/* 机器人数量 */}
              <div className="relative flex-shrink-0">
                <div className="flex items-center gap-2 h-8 px-2">
                  <RobotIcon color="white" className="size-6" />
                  <span className="text-sm text-white">{robotsData.totalRobots}台</span>
                </div>
                <div className="absolute bottom-0 left-2 right-2 h-px bg-green-500" />
              </div>
            </div>
            
            {/* 右侧操作按钮 - 向中间移动 */}
            <div className="flex items-center gap-2 ml-auto mr-16">
              {/* 使用自定义样式覆盖按钮默认高度 */}
              <button className="inline-flex items-center justify-center rounded-full bg-amber-500 hover:bg-amber-600 text-white text-xs px-3 py-0.5 h-6 font-medium">
                批量控制
              </button>
              <button className="inline-flex items-center justify-center rounded-full bg-green-600 hover:bg-green-700 text-white text-xs px-3 py-0.5 h-6 font-medium">
                一键授权
              </button>
              <button className="inline-flex items-center justify-center rounded-full bg-red-600 hover:bg-red-700 text-white text-xs px-3 py-0.5 h-6 font-medium">
                一键关闭
              </button>
            </div>
          </div>
        </div>
        
        {/* 表格区域 */}
        <RobotsTable 
          robots={robotsData.robots}
          totalRobots={robotsData.totalRobots}
          onSelectChange={handleSelectChange}
          onSelectAll={handleSelectAll}
        />
      </div>
      
      {/* 公司信息编辑弹窗 */}
      <CompanyEditDialog
        open={isCompanyDialogOpen}
        onOpenChange={setIsCompanyDialogOpen}
        currentAgentId={undefined}
        onConfirm={(agentId: number, agentName: string) => {
          setCompanyName(agentName);
          console.log("保存公司信息:", { agentId, agentName });
        }}
      />
    </AppLayout>
  );
}
