"use client";

import { useEffect, useState } from "react";
import { useSearchParams } from "next/navigation";
import { AppLayout } from "@/components/app-layout";
import { RobotInfoCard } from "@/components/robot-info-card";
import { AgentRobotsTable } from "@/components/agent-robots-table";
import { getSnCourse } from "@/services/sn";
import { CourseGrade, OrderSnLocation } from "@/types/api";
import { toastService } from "@/services/toast";
import { agentService } from "@/services/agent";

interface RobotInfo {
  sn: string;
  id: string;
  company: string;
  location: string;
  isLocked: boolean;
  courses: Array<{
    id: string;
    name: string;
    authorized: boolean;
  }>;
  robots?: OrderSnLocation[]; // 代理商模式下的机器人列表
}

export default function RobotDetailPage() {
  const searchParams = useSearchParams();
  const [robotInfo, setRobotInfo] = useState<RobotInfo | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const sn = searchParams.get('sn');
    const agentProvince = searchParams.get('agentProvince');
    const agentCity = searchParams.get('agentCity');
    const agentArea = searchParams.get('agentArea');
    const agentId = searchParams.get('agentId');
    const agentName = searchParams.get('agentName');

    // 如果有代理商信息，说明是从代理商页面跳转过来的
    if (agentProvince && agentCity && agentName) {
      loadAgentRobotsInfo({
        province: agentProvince,
        city: agentCity,
        area: agentArea,
        agentId: agentId ? parseInt(agentId) : undefined,
        agentName: agentName
      });
    } else if (sn) {
      // 原有逻辑：通过SN加载单个机器人信息
      loadSnCourseInfo(sn);
    } else {
      toastService.error('缺少必要参数');
      setLoading(false);
      return;
    }
  }, [searchParams]);

  // 加载代理商下的机器人信息
  const loadAgentRobotsInfo = async (agentInfo: {
    province: string;
    city: string;
    area?: string | null;
    agentId?: number;
    agentName: string;
  }) => {
    try {
      setLoading(true);

      // 调用 /agent/detail 接口获取代理商下的机器人信息
      const agentDetail = await agentService.getAgentDetail({
        province: agentInfo.province,
        city: agentInfo.city,
        area: agentInfo.area || undefined,
        agenId: agentInfo.agentId
      });

      // 构建机器人信息对象
      const robotData: RobotInfo = {
        sn: '', // 代理商模式下不显示单个SN
        id: agentInfo.agentId?.toString() || '',
        company: agentInfo.agentName,
        location: [agentInfo.province, agentInfo.city, agentInfo.area].filter(Boolean).join(' '),
        isLocked: false,
        courses: [], // 代理商模式下暂不显示课程信息
        // 添加机器人列表信息
        robots: agentDetail.orderSnLocations || []
      };

      setRobotInfo(robotData);
    } catch (error) {
      console.error('加载代理商机器人信息失败:', error);
      toastService.error('加载代理商机器人信息失败');
    } finally {
      setLoading(false);
    }
  };

  const loadSnCourseInfo = async (sn: string) => {
    try {
      setLoading(true);
      
      // 并行调用三个接口
      const [courses, agentDetail, agentInfo] = await Promise.allSettled([
        // 调用 /sn/course 接口获取课程信息
        getSnCourse({ sn }),
        // 调用 /agent/detail 接口获取代理商位置信息
        agentService.getAgentDetail({ sn }),
        // 调用 /agent/find 接口获取代理商名称信息
        agentService.findAgents({ sn })
      ]);

      // 处理课程信息
      let formattedCourses: Array<{id: string; name: string; authorized: boolean}> = [];
      if (courses.status === 'fulfilled') {
        formattedCourses = courses.value.map((course: CourseGrade) => ({
          id: course.id?.toString() || '',
          name: course.gradeName || '',
          authorized: course.status === 1, // 假设status为1表示已授权
        }));
      } else {
        console.error('获取课程信息失败:', courses.reason);
        toastService.error('获取课程信息失败');
      }

      // 处理代理商信息
      let company = "";
      let location = "";
      
      // 从 /agent/detail 获取位置信息
      if (agentDetail.status === 'fulfilled') {
        const agent = agentDetail.value;
        if (agent.orderSnLocations && agent.orderSnLocations.length > 0) {
          const firstLocation = agent.orderSnLocations[0];
          // 使用省市区信息组合成位置信息
          location = [firstLocation.province, firstLocation.city, firstLocation.area]
            .filter(Boolean)
            .join(' ');
        }
      } else {
        console.error('获取代理商位置信息失败:', agentDetail.reason);
      }
      
      // 从 /agent/find 获取代理商名称
      if (agentInfo.status === 'fulfilled') {
        const agents = agentInfo.value;
        if (agents && agents.length > 0) {
          // 如果有多个代理商记录，取第一个
          company = agents[0].agentName;
          // 如果位置信息为空，也可以从这里获取
          if (!location) {
            location = [agents[0].province, agents[0].city, agents[0].area]
              .filter(Boolean)
              .join(' ');
          }
        }
      } else {
        console.error('获取代理商名称信息失败:', agentInfo.reason);
      }

      // 构建机器人信息对象
      const robotData: RobotInfo = {
        sn: sn,
        id: sn, // 使用SN作为ID
        company: company,
        location: location,
        isLocked: false, // 暂时为false，可以后续从其他接口获取
        courses: formattedCourses,
      };

      setRobotInfo(robotData);
    } catch (error) {
      console.error('加载机器人信息失败:', error);
      toastService.error('加载机器人信息失败');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-white">加载中...</div>
        </div>
      </AppLayout>
    );
  }

  if (!robotInfo) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-white">未找到机器人信息</div>
        </div>
      </AppLayout>
    );
  }

  // 如果有机器人列表，说明是从代理商页面跳转过来的，显示机器人列表表格
  if (robotInfo.robots && robotInfo.robots.length > 0) {
    return (
      <AppLayout>
        <div className="space-y-6">
          <AgentRobotsTable
            agentName={robotInfo.company}
            location={robotInfo.location}
            robots={robotInfo.robots}
          />
        </div>
      </AppLayout>
    );
  }

  // 否则显示单个机器人的详情卡片
  return (
    <AppLayout>
      <div className="space-y-6">
        <RobotInfoCard robotInfo={robotInfo} />
      </div>
    </AppLayout>
  );
}
