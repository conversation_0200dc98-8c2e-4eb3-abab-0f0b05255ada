"use client";

import { useEffect, useState } from "react";
import { useSearchParams } from "next/navigation";
import { AppLayout } from "@/components/app-layout";
import { RobotInfoCard } from "@/components/robot-info-card";
import { getSnCourse } from "@/services/sn";
import { CourseGrade, AgentDetail } from "@/types/api";
import { toastService } from "@/services/toast";
import { agentService } from "@/services/agent";

interface RobotInfo {
  sn: string;
  id: string;
  company: string;
  location: string;
  isLocked: boolean;
  courses: Array<{
    id: string;
    name: string;
    authorized: boolean;
  }>;
}

export default function RobotDetailPage() {
  const searchParams = useSearchParams();
  const [robotInfo, setRobotInfo] = useState<RobotInfo | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const sn = searchParams.get('sn');
    
    if (!sn) {
      toastService.error('缺少SN参数');
      setLoading(false);
      return;
    }

    // 加载SN课程信息
    loadSnCourseInfo(sn);
  }, [searchParams]);

  const loadSnCourseInfo = async (sn: string) => {
    try {
      setLoading(true);
      
      // 并行调用三个接口
      const [courses, agentDetail, agentInfo] = await Promise.allSettled([
        // 调用 /sn/course 接口获取课程信息
        getSnCourse({ sn }),
        // 调用 /agent/detail 接口获取代理商位置信息
        agentService.getAgentDetail({ sn }),
        // 调用 /agent/find 接口获取代理商名称信息
        agentService.findAgents({ sn })
      ]);

      // 处理课程信息
      let formattedCourses: Array<{id: string; name: string; authorized: boolean}> = [];
      if (courses.status === 'fulfilled') {
        formattedCourses = courses.value.map((course: CourseGrade) => ({
          id: course.id?.toString() || '',
          name: course.gradeName || '',
          authorized: course.status === 1, // 假设status为1表示已授权
        }));
      } else {
        console.error('获取课程信息失败:', courses.reason);
        toastService.error('获取课程信息失败');
      }

      // 处理代理商信息
      let company = "";
      let location = "";
      
      // 从 /agent/detail 获取位置信息
      if (agentDetail.status === 'fulfilled') {
        const agent = agentDetail.value;
        if (agent.orderSnLocations && agent.orderSnLocations.length > 0) {
          const firstLocation = agent.orderSnLocations[0];
          // 使用省市区信息组合成位置信息
          location = [firstLocation.province, firstLocation.city, firstLocation.area]
            .filter(Boolean)
            .join(' ');
        }
      } else {
        console.error('获取代理商位置信息失败:', agentDetail.reason);
      }
      
      // 从 /agent/find 获取代理商名称
      if (agentInfo.status === 'fulfilled') {
        const agents = agentInfo.value;
        if (agents && agents.length > 0) {
          // 如果有多个代理商记录，取第一个
          company = agents[0].agentName;
          // 如果位置信息为空，也可以从这里获取
          if (!location) {
            location = [agents[0].province, agents[0].city, agents[0].area]
              .filter(Boolean)
              .join(' ');
          }
        }
      } else {
        console.error('获取代理商名称信息失败:', agentInfo.reason);
      }

      // 构建机器人信息对象
      const robotData: RobotInfo = {
        sn: sn,
        id: sn, // 使用SN作为ID
        company: company,
        location: location,
        isLocked: false, // 暂时为false，可以后续从其他接口获取
        courses: formattedCourses,
      };

      setRobotInfo(robotData);
    } catch (error) {
      console.error('加载机器人信息失败:', error);
      toastService.error('加载机器人信息失败');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-white">加载中...</div>
        </div>
      </AppLayout>
    );
  }

  if (!robotInfo) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-white">未找到机器人信息</div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        <RobotInfoCard robotInfo={robotInfo} />
      </div>
    </AppLayout>
  );
}
