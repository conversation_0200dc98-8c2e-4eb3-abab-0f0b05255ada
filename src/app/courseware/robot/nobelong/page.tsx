"use client";

import { useEffect, useState } from "react";
import { useSearchParams } from "next/navigation";
import { AppLayout } from "@/components/app-layout";
import { NoBelongRobotsList } from "@/components/nobelong-robots-list";
import { agentService } from "@/services/agent";
import { ApiClientError } from "@/services/api";
import { toastService } from "@/services/toast";
import { OrderSnLocation } from "@/types/api";

// 将 OrderSnLocation 转换为 Robot 格式的辅助函数
const convertOrderSnLocationToRobot = (location: OrderSnLocation) => {
  return {
    id: location.robotId,
    sn: location.sn,
    location: location.area || location.city,
    lastContact: "2025年7月14日" // API 没有提供最后联系时间，使用默认值
  };
};

export default function RobotsPage() {
  const searchParams = useSearchParams();
  const [robots, setRobots] = useState<any[]>([]);
  const [totalRobots, setTotalRobots] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 从URL获取参数并调用API
  useEffect(() => {
    const loadAgentDetail = async () => {
      try {
        setLoading(true);
        setError(null);

        // 从URL获取参数
        const province = searchParams.get('province') || '';
        const city = searchParams.get('city') || '';
        const county = searchParams.get('county') || '';
        const query = searchParams.get('query') || '';
        const area = searchParams.get('area') || county; // area 优先，fallback 到 county
        const agentName = searchParams.get('agentName') || query; // agentName 优先，fallback 到 query
        const sn = searchParams.get('sn') || '';
        const stageType = searchParams.get('stageType') as 'UNKNOWN' | 'PRESCHOOL' | 'PRIMARY' || 'UNKNOWN';
        const id = searchParams.get('id') || '';
        const action = searchParams.get('action') || '';

        // 构建API请求参数
        const searchReq = {
          province: province || undefined,
          city: city || undefined,
          area: area || undefined,
          agentName: agentName || undefined,
          sn: sn || undefined,
          stageType: stageType
        };

        // 调试信息
        console.log('nobelong page - URL parameters:', {
          province, city, county, query, area, agentName, sn, stageType, id, action
        });
        console.log('nobelong page - API request:', searchReq);

        // 调用API
        const agentDetail = await agentService.getAgentDetail(searchReq);

        // 转换数据格式
        const convertedRobots = agentDetail.orderSnLocations.map(convertOrderSnLocationToRobot);
        setRobots(convertedRobots);
        setTotalRobots(convertedRobots.length);

      } catch (err) {
        const errorMessage = err instanceof ApiClientError
          ? err.message
          : '加载代理商详情失败';
        setError(errorMessage);
        // 使用toast显示错误
        toastService.error(errorMessage);
        console.error('Failed to load agent detail:', err);
      } finally {
        setLoading(false);
      }
    };

    loadAgentDetail();
  }, [searchParams]);

  if (loading) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-white">加载中...</div>
        </div>
      </AppLayout>
    );
  }

  // 错误已通过toast显示，不再需要单独的错误页面
  if (error) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-muted-foreground">暂无数据</div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        <NoBelongRobotsList
          robots={robots}
          totalRobots={totalRobots}
        />
      </div>
    </AppLayout>
  );
}
