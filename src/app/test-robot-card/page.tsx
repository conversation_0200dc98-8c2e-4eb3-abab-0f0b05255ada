"use client";

import { RobotInfoCard } from "@/components/robot-info-card";

export default function TestRobotCardPage() {
  // 模拟机器人数据
  const mockRobotInfo = {
    sn: "D7DM8365DL",
    id: "10000007893",
    company: "成都一二三科技有限公司",
    location: "四川省成都市金牛区",
    isLocked: false,
    courses: [
      { id: "1", name: "幼儿园大班数学", authorized: true },
      { id: "2", name: "幼儿园大班语文", authorized: true },
      { id: "3", name: "幼儿园大班英语", authorized: false },
      { id: "4", name: "幼儿园大班科学", authorized: true },
      { id: "5", name: "幼儿园大班艺术", authorized: false },
      { id: "6", name: "幼儿园大班健康", authorized: true },
    ]
  };

  // 没有SN的机器人数据（测试初始编辑状态）
  const mockRobotInfoNoSn = {
    sn: "",
    id: "10000007894",
    company: "",
    location: "四川省成都市武侯区",
    isLocked: true,
    courses: [
      { id: "1", name: "小学一年级数学", authorized: false },
      { id: "2", name: "小学一年级语文", authorized: false },
    ]
  };

  return (
    <div className="min-h-screen bg-gray-900 p-8">
      <div className="max-w-6xl mx-auto space-y-8">
        <h1 className="text-2xl font-bold text-white mb-8">Robot Info Card Test</h1>
        
        {/* 测试说明 */}
        <div className="bg-gray-800 p-6 rounded-lg">
          <h2 className="text-lg font-semibold text-white mb-4">SN Modification Test Instructions</h2>
          <div className="text-gray-300 space-y-2">
            <p><strong>Test Scenario 1 (Card with existing SN):</strong></p>
            <ul className="list-disc list-inside ml-4 space-y-1">
              <li>Click the edit icon (✏️) next to the SN</li>
              <li>Modify the SN value</li>
              <li>Click the confirm button (✓)</li>
              <li>API call to /sn/supplement should be made</li>
              <li>On success: SN turns green with checkmark</li>
              <li>On error: Error message appears below input</li>
            </ul>
            
            <p className="mt-4"><strong>Test Scenario 2 (Card without SN):</strong></p>
            <ul className="list-disc list-inside ml-4 space-y-1">
              <li>Card starts in edit mode automatically</li>
              <li>Enter a new SN value</li>
              <li>Click confirm to submit</li>
              <li>Same API behavior as above</li>
            </ul>
            
            <p className="mt-4"><strong>API Details:</strong></p>
            <ul className="list-disc list-inside ml-4 space-y-1">
              <li>Endpoint: POST /sn/supplement</li>
              <li>Payload: {"{"} robotId: string, sn: string {"}"}</li>
              <li>Check browser console for API call logs</li>
            </ul>
          </div>
        </div>
        
        {/* 有SN的机器人卡片 */}
        <div>
          <h2 className="text-lg font-semibold text-white mb-4">Robot with Existing SN</h2>
          <RobotInfoCard robotInfo={mockRobotInfo} />
        </div>
        
        {/* 没有SN的机器人卡片 */}
        <div>
          <h2 className="text-lg font-semibold text-white mb-4">Robot without SN (Auto-edit mode)</h2>
          <RobotInfoCard robotInfo={mockRobotInfoNoSn} />
        </div>
      </div>
    </div>
  );
}
