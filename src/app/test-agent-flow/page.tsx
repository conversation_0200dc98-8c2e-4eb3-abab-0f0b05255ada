"use client";

import { RegionSelector } from "@/components/region-selector";
import Link from "next/link";

export default function TestAgentFlowPage() {
  return (
    <div className="min-h-screen bg-gray-900 p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-2xl font-bold text-white mb-8">Agent Flow Test</h1>
        
        <div className="space-y-8">
          {/* Region Selector */}
          <div>
            <h2 className="text-lg font-semibold text-white mb-4">1. Region Selector (with stageType)</h2>
            <RegionSelector />
          </div>
          
          {/* Test Links */}
          <div>
            <h2 className="text-lg font-semibold text-white mb-4">2. Direct Test Links</h2>
            <div className="space-y-2">
              <div>
                <Link 
                  href="/courseware/agent/list?province=四川省&city=成都市&county=全区域&query=科技&stageType=PRIMARY"
                  className="text-blue-400 hover:text-blue-300 underline"
                >
                  Test with all parameters (四川省, 成都市, 科技, PRIMARY)
                </Link>
              </div>
              <div>
                <Link 
                  href="/courseware/agent/list?stageType=PRESCHOOL"
                  className="text-blue-400 hover:text-blue-300 underline"
                >
                  Test with only stageType (PRESCHOOL)
                </Link>
              </div>
              <div>
                <Link 
                  href="/courseware/agent/list?province=北京市&stageType=UNKNOWN"
                  className="text-blue-400 hover:text-blue-300 underline"
                >
                  Test with province and stageType (北京市, UNKNOWN)
                </Link>
              </div>
            </div>
          </div>
          
          {/* Instructions */}
          <div>
            <h2 className="text-lg font-semibold text-white mb-4">3. Test Instructions</h2>
            <div className="text-gray-300 space-y-2">
              <p>1. Use the Region Selector above to select province and city, enter search query</p>
              <p>2. Add ?stageType=PRIMARY (or PRESCHOOL/UNKNOWN) to the current URL to test with stageType</p>
              <p>3. Click search - it should navigate to /courseware/agent/list with all parameters</p>
              <p>4. The list page should call /agent/find API with the parameters</p>
              <p>5. Or use the direct test links above</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
