"use client";

import Link from "next/link";

export default function TestAgentDetailPage() {
  return (
    <div className="min-h-screen bg-gray-900 p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-2xl font-bold text-white mb-8">Agent Detail Flow Test</h1>
        
        <div className="space-y-8">
          {/* Test Links */}
          <div>
            <h2 className="text-lg font-semibold text-white mb-4">Test Links for Agent Detail Flow</h2>
            <div className="space-y-2">
              <div>
                <Link
                  href="/courseware/agent/detail?agentId=123&agentProvince=四川省&agentCity=成都市&agentArea=金牛区&agentName=成都科技公司&agentBots=25&stageType=PRIMARY&action=detail"
                  className="text-blue-400 hover:text-blue-300 underline bg-green-500/20 px-2 py-1 rounded"
                >
                  <strong>🔗 New Format:</strong> agentId=123, 四川省, 成都市, 金牛区, 成都科技公司, 25台机器人, PRIMARY
                </Link>
              </div>
              <div>
                <Link
                  href="/courseware/agent/detail?agentCompositeId=四川省-成都市-科技公司&stageType=PRIMARY&action=detail"
                  className="text-blue-400 hover:text-blue-300 underline"
                >
                  <strong>🔗 Composite ID Format:</strong> 四川省-成都市-科技公司, PRIMARY (using composite ID)
                </Link>
              </div>
              <div>
                <Link 
                  href="/courseware/agent/detail?province=北京市&city=海淀区&query=教育&stageType=PRESCHOOL&id=agent456&action=detail"
                  className="text-blue-400 hover:text-blue-300 underline"
                >
                  Test with different parameters (北京市, 海淀区, 教育, PRESCHOOL)
                </Link>
              </div>
              <div>
                <Link 
                  href="/courseware/agent/detail?stageType=UNKNOWN&id=agent789&action=detail"
                  className="text-blue-400 hover:text-blue-300 underline"
                >
                  Test with minimal parameters (UNKNOWN stage only)
                </Link>
              </div>
            </div>
          </div>
          
          {/* Complete Flow Test */}
          <div>
            <h2 className="text-lg font-semibold text-white mb-4">Complete Flow Test</h2>
            <div className="space-y-2">
              <div>
                <Link 
                  href="/courseware/agent/list?province=四川省&city=成都市&county=金牛区&query=科技&stageType=PRIMARY"
                  className="text-purple-400 hover:text-purple-300 underline bg-purple-500/20 px-2 py-1 rounded"
                >
                  <strong>🔄 Complete Flow:</strong> Start from Agent List → Click "精细管理" → Should carry all parameters
                </Link>
              </div>
            </div>
          </div>
          
          {/* API Information */}
          <div>
            <h2 className="text-lg font-semibold text-white mb-4">API Information</h2>
            <div className="text-gray-300 space-y-2">
              <p><strong>Endpoint:</strong> POST /agent/course</p>
              <p><strong>Request Body:</strong> CourseAgentSearchReq</p>
              <p><strong>URL Parameters:</strong></p>
              <ul className="list-disc list-inside ml-4 space-y-1">
                <li>agentId - 代理商ID (数字)</li>
                <li>agentProvince - 代理商省份</li>
                <li>agentCity - 代理商城市</li>
                <li>agentArea - 代理商区域</li>
                <li>agentName - 代理商名称</li>
                <li>agentBots - 机器人台数</li>
                <li>stageType - 教育阶段 (UNKNOWN/PRESCHOOL/PRIMARY)</li>
                <li>action - 操作类型 (detail)</li>
              </ul>
              <p><strong>API Parameters:</strong></p>
              <ul className="list-disc list-inside ml-4 space-y-1">
                <li>province - 省份</li>
                <li>city - 城市</li>
                <li>area - 区域</li>
                <li>agenId - 代理商ID (API文档字段名)</li>
                <li>stageType - 教育阶段</li>
                <li>courseId - 课程ID (单个课程授权时需要，一键操作时不传)</li>
              </ul>
              <p><strong>Response:</strong> RListCourseGrade (array of CourseGrade objects)</p>
              <p><strong>CourseGrade Structure:</strong></p>
              <ul className="list-disc list-inside ml-4 space-y-1">
                <li>id: number - 课程等级ID</li>
                <li>gradeName: string - 等级名称</li>
                <li>status: number - 状态 (1=已授权, 0=未授权)</li>
                <li>exp1: string - 扩展字段1</li>
              </ul>
            </div>
          </div>
          
          {/* Instructions */}
          <div>
            <h2 className="text-lg font-semibold text-white mb-4">Test Instructions</h2>
            <div className="text-gray-300 space-y-2">
              <p><strong>1. Parameter Passing Test:</strong></p>
              <ul className="list-disc list-inside ml-4 space-y-1">
                <li>Click any direct test link above</li>
                <li>Check browser console for parameter logs</li>
                <li>Verify all URL parameters are correctly parsed</li>
              </ul>
              
              <p className="mt-4"><strong>2. Complete Flow Test:</strong></p>
              <ul className="list-disc list-inside ml-4 space-y-1">
                <li>Click "Complete Flow" link to go to agent list</li>
                <li>Find any agent with "精细管理" button</li>
                <li>Click "精细管理" button</li>
                <li>Should navigate to detail page with all parameters preserved</li>
              </ul>
              
              <p className="mt-4"><strong>3. API Call Test:</strong></p>
              <ul className="list-disc list-inside ml-4 space-y-1">
                <li>Detail page should call /agent/course API</li>
                <li>API response should be converted to course format</li>
                <li>Courses should display in RobotCoursesCard component</li>
                <li>Check browser console for API call logs</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
