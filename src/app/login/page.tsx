"use client";

import { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import Image from "next/image";
export default function LoginPage() {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false); // 新增状态

  const handleLogin = (e: React.FormEvent) => {
    e.preventDefault();
    // 登录逻辑将在后续实现
    console.log('登录信息:', { username, password });
  };

  return (
    <div className="min-h-screen flex items-center overflow-hidden">
      {/* 渐变背景 */}
      <div className="absolute inset-0 bg-gradient-to-r from-black to-white z-0"></div>

      {/* 左侧Logo和标题区域 */}
      <div className="w-[35vw] flex flex-col items-center justify-center p-8 z-10 ml-[5vw]">
        <div className="w-full">
          <Image
            src="/images/logo_icon.svg"
            alt="Logo"
            width={1000} // 随便填一个大点的数，实际由样式控制
            height={320}
            className="mb-6 w-full h-auto"
            priority
          />
        </div>

      </div>
      {/* 右侧登录表单区域 */}

      <div
        className="h-screen aspect-[0.65]  flex items-center justify-center absolute right-0 top-0  mr-[3vw]"
        style={{
          backgroundImage: "url('/images/head_2.png')",
          backgroundRepeat: "no-repeat",
          backgroundSize: "contain",
          backgroundPosition: "center",
          minWidth: 320
        }}
      >
        <div className="relative w-[70%] h-[20%] px-6 mt-10">

          <form onSubmit={handleLogin} className="space-y-6">
            <div className="space-y-2">
              <div className="relative">
                <span className="absolute left-6  top-1/2 -translate-y-1/2 text-white">账户</span>
                <Input
                  type="text"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  className="w-full pl-18 pr-4 py-3 bg-gradient-to-r from-gray-800/50 to-gray-900/50 border border-white/20 rounded-full pr-4 focus:outline-none focus:ring-2 focus:ring-green-500 text-white"
                  required
                />
              </div>
            </div>

            <div className="space-y-2">
              <div className="relative">
                <span className="absolute left-6  top-1/2 -translate-y-1/2 text-white">密码</span>
                <Input
                  type={showPassword ? "text" : "password"}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full pl-18 pr-4 py-3 bg-gradient-to-r from-gray-800/50 to-gray-900/50 border border-white/20 rounded-full focus:outline-none focus:ring-2 focus:ring-green-500 text-white"
                  required
                />
                <button
                  type="button"
                  className="absolute right-6 top-1/2 -translate-y-1/2 text-white focus:outline-none"
                  onClick={() => setShowPassword((v) => !v)}
                  tabIndex={-1}
                  aria-label={showPassword ? "隐藏密码" : "显示密码"}
                >
                  {showPassword ? (
                    // 眼睛关闭图标
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-5 0-9-4-9-7s4-7 9-7c1.657 0 3.216.417 4.563 1.138M15 12a3 3 0 11-6 0 3 3 0 016 0zm6.364 6.364L4.222 4.222" />
                    </svg>
                  ) : (
                    // 眼睛打开图标
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0zm7 0c0 3-4 7-9 7s-9-4-9-7 4-7 9-7 9 4 9 7z" />
                    </svg>
                  )}
                </button>
              </div>
            </div>

            <div className="flex justify-center">
              <Button
                type="submit"
                className="w-40 h-10 bg-[rgba(110,227,86,1)] hover:bg-[rgba(110,227,86,0.9)] text-white font-bold rounded-full transition-colors"
              >
                登录
              </Button>
            </div>
          </form>

        </div>
      </div>
    </div>

  );
}