"use client";

import { useState } from "react";
import { snService } from "@/services/sn";
import { ApiClientError } from "@/services/api";
import { Button } from "@/components/ui/button";
import { CustomInput } from "@/components/ui/custom-input";

export default function TestSnSupplementPage() {
  const [robotId, setRobotId] = useState("10000007893");
  const [sn, setSn] = useState("D7DM8365DL");
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!robotId.trim() || !sn.trim()) {
      setError("请填写机器人ID和SN码");
      return;
    }

    try {
      setLoading(true);
      setError(null);
      setResult(null);

      await snService.supplementSn({
        robotId: robotId.trim(),
        sn: sn.trim()
      });

      setResult(`成功为机器人 ${robotId} 补充SN: ${sn}`);
    } catch (err) {
      const errorMessage = err instanceof ApiClientError 
        ? err.message 
        : '补充序列号失败';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-900 p-8">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-2xl font-bold text-white mb-8">SN Supplement Test</h1>
        
        <div className="space-y-6">
          {/* Test Form */}
          <div className="bg-gray-800 p-6 rounded-lg">
            <h2 className="text-lg font-semibold text-white mb-4">Test SN Supplement API</h2>
            
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  机器人ID
                </label>
                <CustomInput
                  value={robotId}
                  onChange={(e) => setRobotId(e.target.value)}
                  placeholder="请输入机器人ID"
                  disabled={loading}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  SN码
                </label>
                <CustomInput
                  value={sn}
                  onChange={(e) => setSn(e.target.value)}
                  placeholder="请输入SN码"
                  disabled={loading}
                />
              </div>
              
              <Button 
                type="submit" 
                disabled={loading}
                className="w-full bg-green-600 hover:bg-green-500"
              >
                {loading ? '提交中...' : '补充SN'}
              </Button>
            </form>
            
            {/* Results */}
            {result && (
              <div className="mt-4 p-3 bg-green-900/20 border border-green-500/30 rounded text-green-300">
                {result}
              </div>
            )}
            
            {error && (
              <div className="mt-4 p-3 bg-red-900/20 border border-red-500/30 rounded text-red-300">
                {error}
              </div>
            )}
          </div>
          
          {/* API Information */}
          <div className="bg-gray-800 p-6 rounded-lg">
            <h2 className="text-lg font-semibold text-white mb-4">API Information</h2>
            <div className="text-gray-300 space-y-2">
              <p><strong>Endpoint:</strong> POST /sn/supplement</p>
              <p><strong>Request Body:</strong></p>
              <pre className="bg-gray-900 p-3 rounded text-sm">
{`{
  "robotId": "string",
  "sn": "string"
}`}
              </pre>
              <p><strong>Response:</strong> Standard API response with void data</p>
            </div>
          </div>
          
          {/* Instructions */}
          <div className="bg-gray-800 p-6 rounded-lg">
            <h2 className="text-lg font-semibold text-white mb-4">Test Instructions</h2>
            <div className="text-gray-300 space-y-2">
              <p>1. Enter a robot ID and SN code above</p>
              <p>2. Click "补充SN" to test the API call</p>
              <p>3. Check the result or error message</p>
              <p>4. This simulates the same API call made when confirming SN in the nobelong robots list</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
