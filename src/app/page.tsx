import { AppLayout } from "@/components/app-layout";
import { RobotInfoCard } from "@/components/robot-info-card";
import { Calculate } from "@/components/calculate";


import { AgentManagementPage2 } from "@/components/AgentManagementPage2";
// 仪表盘模拟数据
const calculateInfo = [
  {
  
    id:1,
    title: "幼教市场机器人数量",
    value: "3877台",
    iconUrl:   "/images/robot_icon.svg"
  },
  {
     id:2,
    title: "上课数据统计分析",
    value: "本月累计1722节",
    iconUrl:"/images/up_icon.svg"
  },
  {
     id:3,
    title: "增值课授权占比",
    value: "87%",
    iconUrl: "/images/cicle_icon.svg"
  }
];
export default function Home() {
  return (
    <AppLayout>
      <div className="space-y-6">
        <Calculate calculateInfo={calculateInfo} />

      </div>
    </AppLayout>
  );
}
