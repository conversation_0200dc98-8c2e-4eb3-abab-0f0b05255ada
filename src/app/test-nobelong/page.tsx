"use client";

import Link from "next/link";

export default function TestNoBelongPage() {
  return (
    <div className="min-h-screen bg-gray-900 p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-2xl font-bold text-white mb-8">No Belong Robots Test</h1>
        
        <div className="space-y-8">
          {/* Test Links */}
          <div>
            <h2 className="text-lg font-semibold text-white mb-4">Test Links for /agent/detail API</h2>
            <div className="space-y-2">
              <div>
                <Link 
                  href="/courseware/robot/nobelong?province=四川省&city=成都市&area=金牛区&stageType=PRIMARY"
                  className="text-blue-400 hover:text-blue-300 underline"
                >
                  Test with location parameters (四川省, 成都市, 金牛区, PRIMARY)
                </Link>
              </div>
              <div>
                <Link 
                  href="/courseware/robot/nobelong?agentName=科技&stageType=PRESCHOOL"
                  className="text-blue-400 hover:text-blue-300 underline"
                >
                  Test with agent name (科技, PRESCHOOL)
                </Link>
              </div>
              <div>
                <Link 
                  href="/courseware/robot/nobelong?sn=D7DM8365DL&stageType=UNKNOWN"
                  className="text-blue-400 hover:text-blue-300 underline"
                >
                  Test with SN (D7DM8365DL, UNKNOWN)
                </Link>
              </div>
              <div>
                <Link
                  href="/courseware/robot/nobelong?province=北京市&city=海淀区&agentName=科技有限公司&stageType=PRIMARY"
                  className="text-blue-400 hover:text-blue-300 underline"
                >
                  Test with multiple parameters (北京市, 海淀区, 科技有限公司, PRIMARY)
                </Link>
              </div>
              <div>
                <Link
                  href="/courseware/robot/nobelong?province=四川省&city=成都市&county=金牛区&query=科技&stageType=PRIMARY&id=agent123&action=complete"
                  className="text-blue-400 hover:text-blue-300 underline bg-green-500/20 px-2 py-1 rounded"
                >
                  <strong>🔗 Simulate from Agent List:</strong> 四川省, 成都市, 金牛区, 科技, PRIMARY (with id & action)
                </Link>
              </div>
            </div>
          </div>
          
          {/* API Information */}
          <div>
            <h2 className="text-lg font-semibold text-white mb-4">API Information</h2>
            <div className="text-gray-300 space-y-2">
              <p><strong>Endpoint:</strong> GET /agent/detail (with requestBody)</p>
              <p><strong>Parameters:</strong></p>
              <ul className="list-disc list-inside ml-4 space-y-1">
                <li>province - 省份</li>
                <li>city - 城市</li>
                <li>area - 区域</li>
                <li>agentName - 代理商名称</li>
                <li>sn - 序列号或robotId</li>
                <li>stageType - 阶段类型 (UNKNOWN/PRESCHOOL/PRIMARY)</li>
              </ul>
              <p><strong>Response:</strong> AgentDetail with orderSnLocations array</p>
            </div>
          </div>
          
          {/* Instructions */}
          <div>
            <h2 className="text-lg font-semibold text-white mb-4">Test Instructions</h2>
            <div className="text-gray-300 space-y-2">
              <p>1. Click on any test link above</p>
              <p>2. The page should call /agent/detail API with the URL parameters</p>
              <p>3. The API response (orderSnLocations) should be converted to robot format</p>
              <p>4. The NoBelongRobotsList component should display the robots</p>
              <p>5. Each robot should show: ID, SN, location, and last contact date</p>
              <p><strong>6. Test SN Supplement Flow:</strong></p>
              <ul className="list-disc list-inside ml-4 space-y-1">
                <li>Enter a SN code in any robot row</li>
                <li>Click "确认" (first confirmation)</li>
                <li>Click "确认" again (second confirmation) - this calls /sn/supplement API</li>
                <li>On success: Button shows CheckCheck icon ✓</li>
                <li>If you modify the SN input: Button returns to "确认" text</li>
                <li>On error: Error message appears below input</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
