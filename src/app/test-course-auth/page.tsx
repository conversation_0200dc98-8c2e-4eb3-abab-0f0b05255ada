"use client";

import Link from "next/link";

export default function TestCourseAuthPage() {
  return (
    <div className="min-h-screen bg-gray-900 p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-2xl font-bold text-white mb-8">Course Authorization Test</h1>
        
        <div className="space-y-8">
          {/* Test Links */}
          <div>
            <h2 className="text-lg font-semibold text-white mb-4">Test Links for Course Authorization</h2>
            <div className="space-y-2">
              <div>
                <Link 
                  href="/courseware/agent/detail?province=四川省&city=成都市&county=金牛区&query=科技&stageType=PRIMARY&id=agent123&action=detail"
                  className="text-blue-400 hover:text-blue-300 underline bg-green-500/20 px-2 py-1 rounded"
                >
                  <strong>🔗 Test Course Authorization:</strong> 四川省, 成都市, 金牛区, 科技, PRIMARY
                </Link>
              </div>
              <div>
                <Link 
                  href="/courseware/agent/detail?province=北京市&city=海淀区&county=朝阳区&query=教育&stageType=PRESCHOOL&id=agent456&action=detail"
                  className="text-blue-400 hover:text-blue-300 underline"
                >
                  Test with different parameters (北京市, 海淀区, 朝阳区, 教育, PRESCHOOL)
                </Link>
              </div>
            </div>
          </div>
          
          {/* API Information */}
          <div>
            <h2 className="text-lg font-semibold text-white mb-4">API Information</h2>
            <div className="text-gray-300 space-y-2">
              <p><strong>Enable Course Endpoint:</strong> POST /course-grade/agent/enabled</p>
              <p><strong>Disable Course Endpoint:</strong> POST /course-grade/agent/disabled</p>
              <p><strong>Request Body:</strong> CourseAgentSearchReq</p>
              <p><strong>Parameters:</strong></p>
              <ul className="list-disc list-inside ml-4 space-y-1">
                <li>province - 省份</li>
                <li>city - 城市</li>
                <li>area - 区域 (mapped from county)</li>
                <li>agentName - 代理商名称 (mapped from query/id)</li>
                <li>stageType - 教育阶段 (UNKNOWN/PRESCHOOL/PRIMARY)</li>
                <li>courseId - 课程ID (单个课程授权时需要，一键操作时不传)</li>
              </ul>
              <p><strong>Response:</strong> RVoid (标准API响应)</p>
            </div>
          </div>
          
          {/* Feature Description */}
          <div>
            <h2 className="text-lg font-semibold text-white mb-4">Features Implemented</h2>
            <div className="text-gray-300 space-y-2">
              <p><strong>1. Individual Course Authorization:</strong></p>
              <ul className="list-disc list-inside ml-4 space-y-1">
                <li>Each course has "授权" and "关闭" buttons</li>
                <li>Calls API with specific courseId</li>
                <li>Shows loading state during API call</li>
                <li>Displays success/error toast messages</li>
              </ul>
              
              <p className="mt-4"><strong>2. Batch Authorization:</strong></p>
              <ul className="list-disc list-inside ml-4 space-y-1">
                <li>"一键授权" and "一键关闭" buttons in header</li>
                <li>Calls API without courseId (affects all courses)</li>
                <li>Shows loading state during batch operation</li>
                <li>Disables individual buttons during batch operation</li>
              </ul>
              
              <p className="mt-4"><strong>3. State Management:</strong></p>
              <ul className="list-disc list-inside ml-4 space-y-1">
                <li>Individual course loading states</li>
                <li>Batch operation loading state</li>
                <li>Button disable logic during operations</li>
                <li>Toast notifications for feedback</li>
              </ul>
            </div>
          </div>
          
          {/* Instructions */}
          <div>
            <h2 className="text-lg font-semibold text-white mb-4">Test Instructions</h2>
            <div className="text-gray-300 space-y-2">
              <p><strong>1. Individual Course Authorization:</strong></p>
              <ul className="list-disc list-inside ml-4 space-y-1">
                <li>Click any test link above to go to agent detail page</li>
                <li>Find individual courses in the list</li>
                <li>Click "授权" or "关闭" buttons for specific courses</li>
                <li>Observe loading states and toast messages</li>
                <li>Check browser console for API call logs</li>
              </ul>
              
              <p className="mt-4"><strong>2. Batch Authorization:</strong></p>
              <ul className="list-disc list-inside ml-4 space-y-1">
                <li>Click "一键授权" or "一键关闭" buttons in the header</li>
                <li>Observe that all individual buttons are disabled during batch operation</li>
                <li>Check API calls in browser network tab</li>
              </ul>
              
              <p className="mt-4"><strong>3. Parameter Validation:</strong></p>
              <ul className="list-disc list-inside ml-4 space-y-1">
                <li>Try accessing detail page without required parameters</li>
                <li>Should show error toast about missing parameters</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
