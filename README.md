# EvoConsole Hub - 机器人管理系统

<div align="center">
  <img src="https://images.unsplash.com/photo-1485827404703-89b55fcc595e?w=800&h=400&fit=crop&crop=center" alt="机器人管理系统" width="800" height="400" style="border-radius: 10px; margin-bottom: 20px;">
</div>

一个基于 Next.js 构建的现代化机器人管理系统，提供机器人设备的统一管理、监控和课程授权功能。

## ✨ 功能特性

- 🤖 **机器人信息展示** - 直观的卡片式界面展示机器人详细信息
- 📋 **机器人列表管理** - 高效的列表管理和筛选功能
- 🎓 **课程授权管理** - 灵活的课程授权状态控制
- 📱 **响应式设计** - 完美适配桌面端和移动端
- 🎨 **现代化UI** - 基于 shadcn/ui 的优雅界面设计
- ⚡ **高性能** - Next.js 15 + React 19 提供极致性能体验

## 🛠️ 技术栈

### 核心框架
- **[Next.js 15.4.4](https://nextjs.org/)** - React 全栈框架
- **[React 19.1.0](https://react.dev/)** - 用户界面库
- **[TypeScript 5](https://www.typescriptlang.org/)** - 类型安全的 JavaScript

### UI 组件库
- **[shadcn/ui](https://ui.shadcn.com/)** - 现代化组件库
- **[Radix UI](https://www.radix-ui.com/)** - 无障碍访问的原始组件
- **[Lucide React](https://lucide.dev/)** - 精美的图标库

### 样式方案
- **[Tailwind CSS 4](https://tailwindcss.com/)** - 原子化 CSS 框架
- **[tailwindcss-animate](https://github.com/jamiebuilds/tailwindcss-animate)** - 动画扩展
- **[class-variance-authority](https://cva.style/)** - 组件变体管理

### 开发工具
- **[ESLint](https://eslint.org/)** - 代码质量检查
- **[PostCSS](https://postcss.org/)** - CSS 后处理器
- **[Turbopack](https://turbo.build/pack)** - 高性能构建工具

## 🚀 快速开始

### 环境要求

- Node.js 18.0 或更高版本
- npm、yarn、pnpm 或 bun 包管理器

### 安装依赖

```bash
# 使用 npm
npm install

# 使用 yarn
yarn install

# 使用 pnpm
pnpm install

# 使用 bun
bun install
```

### 启动开发服务器

```bash
# 使用 npm
npm run dev

# 使用 yarn
yarn dev

# 使用 pnpm
pnpm dev

# 使用 bun
bun dev
```

启动后访问 [http://localhost:3000](http://localhost:3000) 查看应用。

### 构建生产版本

```bash
# 构建应用
npm run build

# 启动生产服务器
npm run start
```

## 📁 项目结构

```
evo-console-hub/
├── public/                 # 静态资源文件
│   ├── file.svg
│   ├── globe.svg
│   ├── next.svg
│   ├── vercel.svg
│   └── window.svg
├── src/                    # 源代码目录
│   ├── app/               # Next.js App Router
│   │   ├── globals.css    # 全局样式
│   │   ├── layout.tsx     # 根布局组件
│   │   ├── page.tsx       # 首页
│   │   └── robots/        # 机器人管理页面
│   │       └── page.tsx
│   ├── components/        # React 组件
│   │   ├── ui/           # shadcn/ui 基础组件
│   │   │   ├── accordion.tsx
│   │   │   ├── avatar.tsx
│   │   │   ├── badge.tsx
│   │   │   ├── button.tsx
│   │   │   ├── card.tsx
│   │   │   ├── checkbox.tsx
│   │   │   ├── dropdown-menu.tsx
│   │   │   ├── input.tsx
│   │   │   ├── scroll-area.tsx
│   │   │   ├── select.tsx
│   │   │   ├── separator.tsx
│   │   │   └── table.tsx
│   │   ├── app-layout.tsx      # 应用布局组件
│   │   ├── header.tsx          # 头部组件
│   │   ├── sidebar.tsx         # 侧边栏组件
│   │   ├── robot-info-card.tsx # 机器人信息卡片
│   │   ├── robots-list.tsx     # 机器人列表组件
│   │   └── index.ts           # 组件导出
│   ├── lib/               # 工具函数
│   │   └── utils.ts       # 通用工具函数
│   └── index.css          # 样式入口文件
├── components.json        # shadcn/ui 配置
├── next.config.ts         # Next.js 配置
├── package.json           # 项目依赖配置
├── tailwind.config.ts     # Tailwind CSS 配置
├── tsconfig.json          # TypeScript 配置
└── README.md             # 项目说明文档
```

## 🎯 核心功能

### 机器人管理
- 查看机器人基本信息（SN、ID、公司、位置）
- 机器人列表的筛选和排序
- 批量操作和状态管理

### 课程授权
- 查看机器人的课程授权状态
- 单独调整每个机器人的课程权限
- 授权状态的可视化展示

### 用户界面
- 响应式设计，支持多种设备
- 深色/浅色主题切换
- 流畅的动画和交互效果

## 🔧 开发指南

### 添加新组件

使用 shadcn/ui CLI 添加新的 UI 组件：

```bash
npx shadcn@latest add [component-name]
```

### 自定义样式

项目使用 Tailwind CSS，可以在 `tailwind.config.ts` 中自定义主题：

```typescript
// tailwind.config.ts
export default {
  theme: {
    extend: {
      colors: {
        // 自定义颜色
      }
    }
  }
}
```

### 环境变量

创建 `.env.local` 文件配置环境变量：

```bash
# API 配置
NEXT_PUBLIC_API_URL=your_api_url

# 其他配置
NEXT_PUBLIC_APP_NAME=EvoConsole Hub
```

## 📦 部署

### Vercel 部署（推荐）

1. 将代码推送到 GitHub 仓库
2. 在 [Vercel](https://vercel.com) 中导入项目
3. 配置环境变量
4. 点击部署

### 其他平台

项目支持部署到任何支持 Node.js 的平台：

- **Netlify**: 支持静态导出和服务端渲染
- **Railway**: 简单的容器化部署
- **Docker**: 使用 Dockerfile 进行容器化部署

## 🤝 贡献指南

我们欢迎所有形式的贡献！请遵循以下步骤：

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

### 代码规范

- 使用 TypeScript 进行类型安全开发
- 遵循 ESLint 配置的代码规范
- 组件命名使用 PascalCase
- 文件命名使用 kebab-case

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🔗 相关链接

- [Next.js 文档](https://nextjs.org/docs) - 学习 Next.js 特性和 API
- [React 文档](https://react.dev/) - React 官方文档
- [shadcn/ui](https://ui.shadcn.com/) - 组件库文档
- [Tailwind CSS](https://tailwindcss.com/docs) - 样式框架文档

## 📞 支持

如果您在使用过程中遇到问题，请通过以下方式获取帮助：

- 📧 邮箱：<EMAIL>
- 💬 GitHub Issues：[提交问题](https://github.com/your-username/evo-console-hub/issues)
- 📖 文档：[查看详细文档](https://your-docs-site.com)

---

<div align="center">
  <p>Made with ❤️ by EvoConsole Team</p>
  <p>
    <a href="https://github.com/your-username/evo-console-hub">⭐ Star</a> |
    <a href="https://github.com/your-username/evo-console-hub/fork">🍴 Fork</a> |
    <a href="https://github.com/your-username/evo-console-hub/issues">🐛 Report Bug</a>
  </p>
</div>