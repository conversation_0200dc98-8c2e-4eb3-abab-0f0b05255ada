{"openapi": "3.0.1", "info": {"title": "进化者课件授权平台", "description": "", "version": "1.0.0"}, "tags": [{"name": "序列号管理"}, {"name": "课程管理"}, {"name": "认证管理"}, {"name": "代理商管理"}, {"name": "地区管理"}], "paths": {"/sn/unlock": {"post": {"summary": "解锁", "deprecated": false, "description": "解锁", "operationId": "unlock", "tags": ["序列号管理"], "parameters": [{"name": "sn", "in": "query", "description": "sn码", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RVoid"}}}, "headers": {}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RVoid"}}}, "headers": {}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RVoid"}}}, "headers": {}}}, "security": []}}, "/sn/supplement": {"post": {"summary": "补充序列号", "deprecated": false, "description": "为机器人补充序列号", "operationId": "supplement", "tags": ["序列号管理"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SupplementSnReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RVoid"}}}, "headers": {}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RVoid"}}}, "headers": {}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RVoid"}}}, "headers": {}}}, "security": []}}, "/sn/lock": {"post": {"summary": "锁机", "deprecated": false, "description": "锁机", "operationId": "lock", "tags": ["序列号管理"], "parameters": [{"name": "sn", "in": "query", "description": "sn码", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RVoid"}}}, "headers": {}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RVoid"}}}, "headers": {}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RVoid"}}}, "headers": {}}}, "security": []}}, "/sn/course": {"post": {"summary": "sn课程", "deprecated": false, "description": "查看sn课程授权状态", "operationId": "course", "tags": ["序列号管理"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CourseAgentSearchReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RListCourseGrade"}}}, "headers": {}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RVoid"}}}, "headers": {}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RVoid"}}}, "headers": {}}}, "security": []}}, "/course-grade/enabled": {"post": {"summary": "启用课程", "deprecated": false, "description": "启用指定课程", "operationId": "enabled", "tags": ["课程管理"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CourseReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RVoid"}}}, "headers": {}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RVoid"}}}, "headers": {}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RVoid"}}}, "headers": {}}}, "security": []}}, "/course-grade/disabled": {"post": {"summary": "禁用课程", "deprecated": false, "description": "禁用指定课程", "operationId": "disabled", "tags": ["课程管理"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CourseReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RVoid"}}}, "headers": {}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RVoid"}}}, "headers": {}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RVoid"}}}, "headers": {}}}, "security": []}}, "/course-grade/agent/enabled": {"post": {"summary": "启用代理商课程", "deprecated": false, "description": "为代理商启用指定课程", "operationId": "agent<PERSON><PERSON><PERSON>", "tags": ["课程管理"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CourseAgentSearchReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RVoid"}}}, "headers": {}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RVoid"}}}, "headers": {}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RVoid"}}}, "headers": {}}}, "security": []}}, "/course-grade/agent/disabled": {"post": {"summary": "禁用代理商课程", "deprecated": false, "description": "为代理商禁用指定课程", "operationId": "agentDisabled", "tags": ["课程管理"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CourseAgentSearchReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RVoid"}}}, "headers": {}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RVoid"}}}, "headers": {}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RVoid"}}}, "headers": {}}}, "security": []}}, "/course-grade/list": {"get": {"summary": "获取课程列表", "deprecated": false, "description": "根据教育阶段获取课程列表", "operationId": "list", "tags": ["课程管理"], "parameters": [{"name": "stageType", "in": "query", "description": "教育阶段", "required": true, "schema": {"type": "string", "enum": ["UNKNOWN", "PRESCHOOL", "PRIMARY"]}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RListCourseGrade"}}}, "headers": {}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RVoid"}}}, "headers": {}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RVoid"}}}, "headers": {}}}, "security": []}}, "/auth/logout": {"post": {"summary": "用户登出", "deprecated": false, "description": "用户登出清除会话", "operationId": "logout", "tags": ["认证管理"], "parameters": [], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RVoid"}}}, "headers": {}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RVoid"}}}, "headers": {}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RVoid"}}}, "headers": {}}}, "security": []}}, "/auth/login": {"post": {"summary": "用户登录", "deprecated": false, "description": "用户登录获取访问令牌（使用Spring Security处理）", "operationId": "login", "tags": ["认证管理"], "parameters": [], "responses": {"200": {"description": "登录成功", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}, "headers": {}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RVoid"}}}, "headers": {}}, "401": {"description": "登录失败", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}, "headers": {}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RVoid"}}}, "headers": {}}}, "security": []}}, "/agent/transfer": {"post": {"summary": "代理商转移", "deprecated": false, "description": "转移代理商到指定客户", "operationId": "transfer", "tags": ["代理商管理"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AgentTransferReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RVoid"}}}, "headers": {}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RVoid"}}}, "headers": {}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RVoid"}}}, "headers": {}}}, "security": []}}, "/agent/course": {"post": {"summary": "代理商课程", "deprecated": false, "description": "查看代理商课程授权状态", "operationId": "agentCourse", "tags": ["代理商管理"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CourseAgentSearchReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RListCourseGrade"}}}, "headers": {}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RVoid"}}}, "headers": {}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RVoid"}}}, "headers": {}}}, "security": []}}, "/agent/list": {"get": {"summary": "获取代理商列表", "deprecated": false, "description": "根据阶段类型获取代理商列表", "operationId": "list_1", "tags": ["代理商管理"], "parameters": [{"name": "stageType", "in": "query", "description": "阶段类型", "required": true, "schema": {"type": "string", "enum": ["UNKNOWN", "PRESCHOOL", "PRIMARY"]}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RListAgentUsedName"}}}, "headers": {}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RVoid"}}}, "headers": {}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RVoid"}}}, "headers": {}}}, "security": []}}, "/agent/find": {"post": {"summary": "查询代理商", "deprecated": false, "description": "根据条件查询代理商列表", "operationId": "find", "tags": ["代理商管理"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CourseAgentSearchReq"}, "examples": {}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RListAgentVO"}}}, "headers": {}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RVoid"}}}, "headers": {}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RVoid"}}}, "headers": {}}}, "security": []}}, "/agent/detail": {"post": {"summary": "获取代理商详情", "deprecated": false, "description": "根据条件获取代理商详细信息", "operationId": "detail", "tags": ["代理商管理"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CourseAgentSearchReq"}, "examples": {}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RAgentDetail"}}}, "headers": {}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RVoid"}}}, "headers": {}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RVoid"}}}, "headers": {}}}, "security": []}}, "/region/province": {"get": {"summary": "获取省份列表", "deprecated": false, "description": "获取所有省份信息", "operationId": "findProv<PERSON>ce", "tags": ["地区管理"], "parameters": [], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RListString"}}}, "headers": {}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RVoid"}}}, "headers": {}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RVoid"}}}, "headers": {}}}, "security": []}}, "/region/city": {"get": {"summary": "获取城市列表", "deprecated": false, "description": "根据省份获取城市列表", "operationId": "findCity", "tags": ["地区管理"], "parameters": [{"name": "province", "in": "query", "description": "省份名称", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RListString"}}}, "headers": {}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RVoid"}}}, "headers": {}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RVoid"}}}, "headers": {}}}, "security": []}}, "/region/area": {"get": {"summary": "获取区域列表", "deprecated": false, "description": "根据省份和城市获取区域列表", "operationId": "find<PERSON>rea", "tags": ["地区管理"], "parameters": [{"name": "province", "in": "query", "description": "省份名称", "required": true, "schema": {"type": "string"}}, {"name": "city", "in": "query", "description": "城市名称", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RListString"}}}, "headers": {}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RVoid"}}}, "headers": {}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RVoid"}}}, "headers": {}}}, "security": []}}}, "components": {"schemas": {"RVoid": {"type": "object", "description": "统一响应结果", "properties": {"code": {"type": "integer", "format": "int32", "description": "响应码", "example": 0}, "message": {"type": "string", "description": "响应消息", "example": "操作成功"}, "data": {"description": "响应数据"}, "timestamp": {"type": "integer", "format": "int64", "description": "时间戳", "example": 1640995200000}, "success": {"type": "boolean"}}}, "R": {"type": "object", "description": "统一响应结果", "properties": {"code": {"type": "integer", "format": "int32", "description": "响应码", "example": 0}, "message": {"type": "string", "description": "响应消息", "example": "操作成功"}, "data": {"description": "响应数据"}, "timestamp": {"type": "integer", "format": "int64", "description": "时间戳", "example": 1640995200000}, "success": {"type": "boolean"}}}, "SupplementSnReq": {"type": "object", "description": "补充序列号请求参数", "properties": {"robotId": {"type": "string", "description": "机器人ID"}, "sn": {"type": "string", "description": "序列号"}}}, "CourseAgentSearchReq": {"type": "object", "description": "查询条件", "properties": {"province": {"type": "string", "description": "省份"}, "city": {"type": "string", "description": "城市"}, "area": {"type": "string", "description": "区域"}, "agenId": {"type": "integer", "format": "int64", "description": "代理商名称 missingSn:暂无信息 unassigned:暂无归属"}, "sn": {"type": "string", "description": "序列号或robotId"}, "stageType": {"type": "string", "description": "阶段类型", "enum": ["UNKNOWN", "PRESCHOOL", "PRIMARY"]}, "courseId": {"type": "integer", "format": "int64", "description": "课程ID"}, "robotIds": {"type": "array", "description": "机器ID", "items": {"type": "string"}}}}, "CourseGrade": {"type": "object", "description": "课程等级信息", "properties": {"id": {"type": "integer", "format": "int64", "description": "课程等级ID"}, "gradeName": {"type": "string", "description": "等级名称"}, "status": {"type": "integer", "format": "int32", "description": "状态"}, "exp1": {"type": "string", "description": "扩展字段1"}}}, "RListCourseGrade": {"type": "object", "description": "统一响应结果", "properties": {"code": {"type": "integer", "format": "int32", "description": "响应码", "example": 0}, "message": {"type": "string", "description": "响应消息", "example": "操作成功"}, "data": {"type": "array", "description": "响应数据", "items": {"$ref": "#/components/schemas/CourseGrade"}}, "timestamp": {"type": "integer", "format": "int64", "description": "时间戳", "example": 1640995200000}, "success": {"type": "boolean"}}}, "CourseReq": {"type": "object", "description": "课程请求参数", "properties": {"stageType": {"type": "string", "description": "阶段类型", "enum": ["UNKNOWN", "PRESCHOOL", "PRIMARY"]}, "courseGradeId": {"type": "integer", "format": "int64", "description": "课程等级ID"}}}, "AgentTransferReq": {"type": "object", "description": "转移请求参数", "properties": {"stageType": {"type": "string", "description": "阶段类型", "enum": ["UNKNOWN", "PRESCHOOL", "PRIMARY"]}, "agentId": {"type": "integer", "format": "int64", "description": "代理商ID"}, "snList": {"type": "array", "description": "序列号列表", "items": {"type": "string"}}}}, "RListString": {"type": "object", "description": "统一响应结果", "properties": {"code": {"type": "integer", "format": "int32", "description": "响应码", "example": 0}, "message": {"type": "string", "description": "响应消息", "example": "操作成功"}, "data": {"type": "array", "description": "响应数据", "items": {"type": "string"}}, "timestamp": {"type": "integer", "format": "int64", "description": "时间戳", "example": 1640995200000}, "success": {"type": "boolean"}}}, "AgentUsedName": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "agentsId": {"type": "integer", "format": "int64"}, "usedName": {"type": "string"}}}, "RListAgentUsedName": {"type": "object", "description": "统一响应结果", "properties": {"code": {"type": "integer", "format": "int32", "description": "响应码", "example": 0}, "message": {"type": "string", "description": "响应消息", "example": "操作成功"}, "data": {"type": "array", "description": "响应数据", "items": {"$ref": "#/components/schemas/AgentUsedName"}}, "timestamp": {"type": "integer", "format": "int64", "description": "时间戳", "example": 1640995200000}, "success": {"type": "boolean"}}}, "AgentVO": {"type": "object", "description": "代理商视图对象", "properties": {"agentId": {"type": "integer", "format": "int64", "description": "代理商Id"}, "agentName": {"type": "string", "description": "代理商名称"}, "province": {"type": "string", "description": "省份"}, "city": {"type": "string", "description": "城市"}, "area": {"type": "string", "description": "区域"}, "region": {"type": "string", "description": "不需要"}, "bots": {"type": "integer", "format": "int32", "description": "机器人数量"}}}, "RListAgentVO": {"type": "object", "description": "统一响应结果", "properties": {"code": {"type": "integer", "format": "int32", "description": "响应码", "example": 0}, "message": {"type": "string", "description": "响应消息", "example": "操作成功"}, "data": {"type": "array", "description": "响应数据", "items": {"$ref": "#/components/schemas/AgentVO"}}, "timestamp": {"type": "integer", "format": "int64", "description": "时间戳", "example": 1640995200000}, "success": {"type": "boolean"}}}, "AgentDetail": {"type": "object", "description": "代理商详情", "properties": {"orderSnLocations": {"type": "array", "description": "订单序列号位置列表", "items": {"$ref": "#/components/schemas/OrderSnLocation"}}}}, "OrderSnLocation": {"type": "object", "description": "订单序列号位置信息", "properties": {"id": {"type": "integer", "format": "int64", "description": "ID"}, "robotId": {"type": "string", "description": "机器人ID"}, "province": {"type": "string", "description": "省份"}, "city": {"type": "string", "description": "城市"}, "area": {"type": "string", "description": "区域"}, "sn": {"type": "string", "description": "序列号"}}}, "RAgentDetail": {"type": "object", "description": "统一响应结果", "properties": {"code": {"type": "integer", "format": "int32", "description": "响应码", "example": 0}, "message": {"type": "string", "description": "响应消息", "example": "操作成功"}, "data": {"$ref": "#/components/schemas/AgentDetail", "description": "响应数据"}, "timestamp": {"type": "integer", "format": "int64", "description": "时间戳", "example": 1640995200000}, "success": {"type": "boolean"}}}}, "securitySchemes": {}}, "servers": [], "security": []}